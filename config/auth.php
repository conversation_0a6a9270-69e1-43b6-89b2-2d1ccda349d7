<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2015 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

//think-auth 配置文件

return [
    'default'          => 'admin',
    'guards'           => [
        'admin' => [
            'type'     => 'token',
            'provider' => 'admin',
        ],
        'api'   => [
            'type'     => 'token',
            'provider' => 'user',
        ],
    ],
    'providers'        => [
        'admin' => [
            'type' => \app\lib\AdminProvider::class,
        ],
        'user'  => [
            'type' => \app\lib\UserProvider::class,
        ],
    ],
    'password'         => [
        'provider' => 'user',
    ],
    //设为false,则不注册路由
    'route'            => false,
    'policy_namespace' => '\\app\\policy\\',
    'policies'         => [],
];
