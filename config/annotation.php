<?php

use yunwuxin\auth\middleware\UseGuard;

return [
    'inject' => [
        'enable'     => true,
        'namespaces' => [],
    ],
    'route'  => [
        'enable'      => true,
        'controllers' => [
            app_path('controller/admin') => [
                'name'       => 'admin',
                'middleware' => [],
            ],
            app_path('controller/api')   => [
                'name'       => 'api',
                'middleware' => [
                    [UseGuard::class, ['api']],
                ],
            ],
        ],
    ],
    'model'  => [
        'enable' => true,
    ],
    'ignore' => [],
];
