<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddReservationFeature extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        // 添加can_reserve字段到eatery表
        $this->table('eatery')
            ->addColumn(Column::boolean('can_reserve')->setDefault(0)->setComment('是否支持预订：0=不支持，1=支持')
                ->setAfter('type'))
            ->update();

        // 创建reservation表
        $this->table('reservation')
            ->addColumn(Column::integer('eatery_id')->setComment('餐厅ID'))
            ->addColumn(Column::integer('user_id')->setComment('用户ID'))
            ->addColumn(Column::tinyInteger('status')->setDefault(0)
                ->setComment('预订状态：0=确认中，1=已确认，2=已取消（商家取消）'))
            ->addTimestamps()
            ->addIndex(['eatery_id'])
            ->addIndex(['user_id'])
            ->addIndex(['create_time'])
            ->create();
    }
}
