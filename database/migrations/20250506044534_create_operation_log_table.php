<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateOperationLogTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('operation_log')
            ->addColumn(Column::integer('user_id')->setComment('用户ID'))
            ->addColumn(Column::string('user_type')->setComment('用户类型：admin=管理员，merchant=商家'))
            ->addColumn(Column::string('action')->setComment('操作类型'))
            ->addColumn(Column::string('module')->setComment('操作模块'))
            ->addColumn(Column::integer('target_id')->setNullable()->setComment('操作对象ID'))
            ->addColumn(Column::string('target_type')->setNullable()->setComment('操作对象类型'))
            ->addColumn(Column::text('content')->setComment('操作内容'))
            ->addColumn(Column::string('ip')->setNullable()->setComment('IP地址'))
            ->addColumn(Column::string('user_agent')->setNullable()->setComment('User Agent'))
            ->addTimestamps()
            ->create();
    }
}
