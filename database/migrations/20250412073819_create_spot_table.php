<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSpotTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('spot')
            ->addColumn(Column::string('name'))
            ->addColumn(Column::json('images'))
            ->addColumn(Column::json('hours'))
            ->addColumn(Column::json('location'))
            ->addColumn(Column::text('address'))
            ->addColumn(Column::json('tickets'))
            ->addColumn(Column::integer('views')->setDefault(0))
            ->addColumn(Column::integer('likes')->setDefault(0))
            ->addColumn(Column::integer('stars')->setDefault(0))
            ->addColumn(Column::integer('rec')->setDefault(0)->setComment('推荐值'))
            ->addColumn(Column::integer('ord')->setDefault(0)->setComment('排序值'))
            ->addColumn(Column::tinyInteger('status')->setDefault(1))
            ->addTimestamps()
            ->create();

        $this->table('spot_like')
            ->setId(false)
            ->addColumn(Column::integer('spot_id')->setComment('景区ID'))
            ->addColumn(Column::integer('user_id')->setComment('用户ID'))
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP')->setComment('点赞时间'))
            ->addIndex(['spot_id', 'user_id'], ['unique' => true])
            ->create();

        $this->table('spot_star')
            ->setId(false)
            ->addColumn(Column::integer('spot_id')->setComment('景区ID'))
            ->addColumn(Column::integer('user_id')->setComment('用户ID'))
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP')->setComment('收藏时间'))
            ->addIndex(['spot_id', 'user_id'], ['unique' => true])
            ->create();
    }
}
