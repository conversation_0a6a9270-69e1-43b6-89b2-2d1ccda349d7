<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateCouponTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('coupon')
            ->addColumn(Column::string('name')->setComment('优惠券名称'))
            ->addColumn(Column::integer('amount')->setComment('优惠券金额'))
            ->addColumn(Column::integer('nums')->setDefault(0))
            ->addColumn(Column::integer('claimed')->setDefault(0))
            ->addColumn(Column::timestamp('start_time')->setNullable()->setComment('开始时间'))
            ->addColumn(Column::timestamp('end_time')->setNullable()->setComment('结束时间'))
            ->addColumn(Column::tinyInteger('status')->setDefault(1))
            ->addTimestamps()
            ->create();

        $this->table('user_coupon')
            ->addColumn(Column::integer('user_id')->setComment('用户ID'))
            ->addColumn(Column::integer('coupon_id')->setComment('优惠券ID'))
            ->addColumn(Column::integer('eatery_id')->setComment('店铺ID')->setNullable())
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP')->setComment('领取时间'))
            ->addColumn(Column::timestamp('expire_time')->setNullable()->setComment('过期时间'))
            ->addColumn(Column::timestamp('use_time')->setNullable()->setComment('使用时间'))
            ->addIndex(['coupon_id'])
            ->addIndex(['user_id'])
            ->create();
    }
}
