<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSpotProjectTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('spot_project')
            ->addColumn(Column::integer('spot_id')->setComment('景点ID'))
            ->addColumn(Column::string('name')->setComment('项目名称'))
            ->addColumn(Column::string('icon')->setNullable()->setComment('项目图标'))
            ->addColumn(Column::decimal('price', 10, 2)->setComment('项目价格'))
            ->addTimestamps()
            ->addIndex(['spot_id'])
            ->create();
    }
}
