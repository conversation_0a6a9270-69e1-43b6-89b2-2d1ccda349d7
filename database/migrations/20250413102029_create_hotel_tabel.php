<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateHotelTabel extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('hotel')
            ->addColumn(Column::string('name'))
            ->addColumn(Column::string('type'))
            ->addColumn(Column::json('images'))
            ->addColumn(Column::json('location'))
            ->addColumn(Column::text('address'))
            ->addColumn(Column::json('rating'))
            ->addColumn(Column::integer('price'))
            ->addColumn(Column::string('path')->setComment('携程地址'))
            ->addColumn(Column::integer('rec')->setDefault(0)->setComment('推荐值'))
            ->addColumn(Column::integer('ord')->setDefault(0)->setComment('排序值'))
            ->addColumn(Column::tinyInteger('status')->setDefault(1))
            ->addTimestamps()
            ->create();
    }
}
