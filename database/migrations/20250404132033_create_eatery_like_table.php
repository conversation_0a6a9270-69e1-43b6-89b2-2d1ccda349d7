<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateEateryLikeTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('eatery_like')
            ->setId(false)
            ->addColumn(Column::integer('eatery_id')->setComment('店铺ID'))
            ->addColumn(Column::integer('user_id')->setComment('用户ID'))
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP')->setComment('点赞时间'))
            ->addIndex(['eatery_id', 'user_id'], ['unique' => true])
            ->create();

        $this->table('eatery_star')
            ->setId(false)
            ->addColumn(Column::integer('eatery_id')->setComment('店铺ID'))
            ->addColumn(Column::integer('user_id')->setComment('用户ID'))
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP')->setComment('收藏时间'))
            ->addIndex(['eatery_id', 'user_id'], ['unique' => true])
            ->create();
    }
}
