<?php

use think\migration\Migrator;
use think\migration\db\Column;

class MigrateSpotTicketsToProjects extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        // 迁移现有的tickets数据到spot_project表
        $this->execute("
            INSERT INTO spot_project (spot_id, name, price, create_time, update_time)
            SELECT
                s.id as spot_id,
                JSON_UNQUOTE(JSON_EXTRACT(ticket.value, '$.name')) as name,
                CAST(JSON_UNQUOTE(JSON_EXTRACT(ticket.value, '$.price')) AS DECIMAL(10,2)) as price,
                s.create_time,
                s.update_time
            FROM spot s
            CROSS JOIN JSON_TABLE(
                s.tickets,
                '$[*]' COLUMNS (
                    value JSON PATH '$'
                )
            ) AS ticket
            WHERE s.tickets IS NOT NULL
            AND JSON_VALID(s.tickets) = 1
            AND JSON_LENGTH(s.tickets) > 0
        ");

        // 移除spot表的tickets字段
        $this->table('spot')
            ->removeColumn('tickets')
            ->update();
    }
}
