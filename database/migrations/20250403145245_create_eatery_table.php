<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateEateryTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('eatery')
            ->addColumn(Column::string('name'))
            ->addColumn(Column::json('images'))
            ->addColumn(Column::json('hours'))
            ->addColumn(Column::integer('cost'))
            ->addColumn(Column::json('location'))
            ->addColumn(Column::text('address'))
            ->addColumn(Column::json('tags'))
            ->addColumn(Column::integer('views')->setDefault(0))
            ->addColumn(Column::integer('likes')->setDefault(0))
            ->addColumn(Column::integer('stars')->setDefault(0))
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addTimestamps()
            ->create();

        $this->table('eatery_member')
            ->setId(false)
            ->addColumn(Column::integer('eatery_id'))
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::tinyInteger('access_level'))
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->addIndex(['user_id'], ['unique' => true])
            ->create();
    }
}
