<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddPolymorphicToUserCoupon extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        // 添加多态关联字段
        $this->table('user_coupon')
            ->addColumn(Column::string('redeemable_type')->setNullable()->setComment('核销商家类型')->setAfter('eatery_id'))
            ->addColumn(Column::integer('redeemable_id')->setNullable()->setComment('核销商家ID')->setAfter('redeemable_type'))
            ->addIndex(['redeemable_type', 'redeemable_id'])
            ->update();

        // 迁移现有数据：将eatery_id数据转换为多态关联
        $this->execute("
            UPDATE user_coupon
            SET redeemable_type = 'app\\\\model\\\\Eatery', redeemable_id = eatery_id
            WHERE eatery_id IS NOT NULL
        ");
    }
}
