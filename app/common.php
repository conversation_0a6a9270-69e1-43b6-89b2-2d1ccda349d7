<?php
// 应用公共文件

/**
 * 根据坐标获取区县信息
 * @param float $lat 纬度
 * @param float $lng 经度
 * @return string|null 区县名称，获取失败返回null
 */
function get_district($lat, $lng)
{
    // 腾讯地图API密钥
    $key = 'W6PBZ-2OWWC-V6N27-A7I3B-2PF3Z-RJB55'; // 请替换为您的实际密钥

    try {
        // 使用Guzzle发送HTTP请求
        $client = new \GuzzleHttp\Client();
        $response = $client->get('https://apis.map.qq.com/ws/geocoder/v1/', [
            'query' => [
                'location' => $lat . ',' . $lng,
                'key' => $key
            ]
        ]);

        // 获取响应内容
        $body = $response->getBody()->getContents();

        // 解析JSON响应
        $data = json_decode($body, true);

        // 检查响应状态
        if (!isset($data['status']) || $data['status'] !== 0) {
            return null;
        }

        // 提取区县信息
        if (isset($data['result']['address_component']['district'])) {
            return $data['result']['address_component']['district'];
        }
    } catch (\Exception $e) {
        // 异常处理
        return null;
    }

    return null;
}
