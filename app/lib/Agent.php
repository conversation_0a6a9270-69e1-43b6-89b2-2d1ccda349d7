<?php

namespace app\lib;

use Exception;
use Guz<PERSON><PERSON>ttp\Client;
use Guz<PERSON><PERSON>ttp\HandlerStack;
use GuzzleHttp\Utils;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamInterface;
use think\helper\Arr;
use think\helper\Str;

class Agent
{
    protected $url = 'https://zhenze-huhehaote.cmecloud.cn/inference-api/exp-api/inf-1336844822260682752/v1/chat/completions';

    public function __construct()
    {
    }

    public function chat($messages, $stream = true)
    {
        $res = $this->getClient()->post($this->url, [
            'json'   => [
                'messages' => $messages,
                'stream'   => $stream,
            ],
            'stream' => $stream,
        ]);

        $res = $this->transformResponse($res);

        if ($res instanceof StreamInterface) {
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }
                $event   = json_decode($data, true);
                $content = Arr::get($event, 'choices.0.delta.content', '');
                if (strlen($content) > 0) {
                    yield $content;
                }
            }
        } else {
            return Arr::get($res, 'choices.0.message.content', '');
        }
    }

    public function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        if (!$isOk) {
            $content = $response->getBody()->getContents();
            $result  = json_decode($content, true);
            if ($result) {
                throw new Exception($result['error']['message']);
            }
            throw new Exception('Unknown error');
        }

        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        if (!Str::contains($contentType, 'json')) {
            return $response;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    protected function getMessages(StreamInterface $response)
    {
        $buffer  = '';
        $message = null;
        while (!$response->eof()) {
            $text = $response->read(1);
            if ($text == "\r") {
                continue;
            }
            $buffer .= $text;
            if ($text == "\n") {
                if (empty($message)) {
                    $message = ['id' => '', 'event' => '', 'data' => ''];
                }
                if ($buffer == "\n") {
                    yield $message;
                    $message = null;
                } else {
                    if (preg_match('/data:(?<data>.*)/', trim($buffer), $match)) {
                        $message['data'] = trim($match['data']);
                    }
                }
                $buffer = '';
            }
        }
    }

    protected function getClient()
    {
        $handler = new HandlerStack(Utils::chooseHandler());
        $token   = env('AI_TOKEN', 'OSEYv8377gy2-r57Kl2z4n0g8RNDBihcl55KGBJLAYU');

        return new Client([
            'handler' => $handler,
            'verify'  => false,
            'headers' => [
                'Authorization' => "Bearer {$token}",
            ],
        ]);
    }
}
