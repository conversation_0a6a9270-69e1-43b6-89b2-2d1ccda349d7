<?php

namespace app\lib;

use app\model\Admin;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Throwable;
use yunwuxin\auth\credentials\TokenCredentials;
use yunwuxin\auth\interfaces\Provider;

class AdminProvider implements Provider
{
    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof TokenCredentials) {

            $token = $credentials->getToken();

            try {
                $decoded = (array) JWT::decode($token, new Key(config('app.token'), 'HS256'));
                $id      = $decoded['id'];

                return Admin::findOrFail($id);
            } catch (Throwable) {
            }
        }
    }
}
