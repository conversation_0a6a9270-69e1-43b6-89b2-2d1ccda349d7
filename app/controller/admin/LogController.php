<?php

namespace app\controller\admin;

use app\model\OperationLog;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use yunwuxin\auth\middleware\Authentication;

/**
 * 日志管理控制器
 */
#[Middleware(Authentication::class)]
class LogController extends Controller
{
    /**
     * 管理员操作日志列表
     */
    #[Get('logs/admin')]
    public function adminLogs()
    {
        // 获取管理员操作日志
        return OperationLog::with(['admin'])
            ->where('user_type', OperationLog::USER_TYPE_ADMIN)
            ->order('create_time', 'desc')
            ->paginate();
    }

    /**
     * 商户操作日志列表
     */
    #[Get('logs/merchant')]
    public function merchantLogs()
    {
        // 获取商户操作日志
        return OperationLog::with(['user'])
            ->where('user_type', OperationLog::USER_TYPE_MERCHANT)
            ->order('create_time', 'desc')
            ->paginate();
    }
}
