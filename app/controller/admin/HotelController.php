<?php

namespace app\controller\admin;

use app\model\Hotel;
use app\model\OperationLog;
use app\model\UserCoupon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use think\facade\Filesystem;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class HotelController extends Controller
{
    /**
     * 获取酒店列表（已审核通过的酒店，包括正常和禁用状态）
     */
    #[Get('hotel')]
    public function index()
    {
        $query = Hotel::whereIn('status', [Hotel::STATUS_NORMAL, Hotel::STATUS_DISABLED])
            ->append(['owner'])
            ->order('id', 'desc');

        $this->searchField($query, 'name');

        return $query->paginate();
    }

    /**
     * 获取待审核的酒店列表
     */
    #[Get('hotel/pending')]
    public function pending()
    {
        return Hotel::where('status', Hotel::STATUS_PENDING)
            ->append(['owner'])
            ->order('id', 'desc')
            ->paginate();
    }

    /**
     * 创建酒店
     */
    #[Post('hotel')]
    public function save()
    {
        $data = $this->validateHotelData();

        $hotel = Hotel::create($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_HOTEL,
            "创建酒店 {$data['name']}",
            $hotel->id,
            'hotel'
        );

        return $hotel;
    }

    /**
     * 获取酒店详情
     */
    #[Get('hotel/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        // 获取酒店信息，包括关联的用户信息
        $hotel = Hotel::with(['members'])->findOrFail($id);

        return $hotel;
    }

    /**
     * 审核酒店（通过）
     */
    #[Post('hotel/:id/approve')]
    #[Pattern('id', '\d+')]
    public function approve($id)
    {
        $hotel = Hotel::where('status', Hotel::STATUS_PENDING)->findOrFail($id);

        $hotel->save(['status' => Hotel::STATUS_NORMAL]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_APPROVE,
            OperationLog::MODULE_HOTEL,
            "审核通过酒店",
            $hotel->id,
            'hotel'
        );

        return json(['message' => '审核通过']);
    }

    /**
     * 审核酒店（拒绝）
     */
    #[Post('hotel/:id/reject')]
    #[Pattern('id', '\d+')]
    public function reject($id)
    {
        $hotel = Hotel::where('status', Hotel::STATUS_PENDING)->findOrFail($id);

        $hotel->save(['status' => Hotel::STATUS_REJECTED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_REJECT,
            OperationLog::MODULE_HOTEL,
            "拒绝酒店审核",
            $hotel->id,
            'hotel'
        );

        return json(['message' => '已拒绝']);
    }

    /**
     * 更新酒店信息
     */
    #[Put('hotel/:id')]
    #[Pattern('id', '\d+')]
    public function update($id)
    {
        $hotel = Hotel::findOrFail($id);

        $data = $this->validateHotelData();

        $hotel->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "更新酒店 {$hotel->name}",
            $hotel->id,
            'hotel'
        );

        return $hotel;
    }

    /**
     * 获取酒店的员工列表
     */
    #[Get('hotel/:id/members')]
    #[Pattern('id', '\d+')]
    public function members($id)
    {
        // 验证酒店是否存在
        $hotel = Hotel::findOrFail($id);

        $members = $hotel->members()
            ->wherePivot('access_level', \app\model\HotelMember::MEMBER)
            ->order('pivot.create_time', 'asc')
            ->select();

        return $members;
    }

    /**
     * 获取酒店消费券核销记录
     */
    #[Get('hotel/:id/coupons')]
    #[Pattern('id', '\d+')]
    public function coupons($id)
    {
        // 验证酒店是否存在并获取实例
        $hotel = Hotel::findOrFail($id);

        // 使用关联方式获取酒店的所有消费券核销记录
        return $hotel->coupons()
            ->with(['info', 'user'])
            ->whereNotNull('use_time')
            ->order('use_time', 'desc')
            ->paginate();
    }

    /**
     * 删除酒店
     */
    #[Delete('hotel/:id')]
    #[Pattern('id', '\d+')]
    public function delete($id)
    {
        $hotel = Hotel::findOrFail($id);

        $hotel->delete();

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_DELETE,
            OperationLog::MODULE_HOTEL,
            "删除酒店",
            $id,
            'hotel'
        );
    }

    /**
     * 设置酒店推荐值
     */
    #[Put('hotel/:id/rec')]
    #[Pattern('id', '\d+')]
    public function setRec($id)
    {
        $hotel = Hotel::findOrFail($id);

        $data = $this->validate([
            'rec|推荐值' => 'require|integer',
        ]);

        $hotel->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "更新酒店推荐值",
            $hotel->id,
            'hotel'
        );

        return $hotel;
    }

    /**
     * 设置酒店排序值
     */
    #[Put('hotel/:id/ord')]
    #[Pattern('id', '\d+')]
    public function setOrd($id)
    {
        $hotel = Hotel::findOrFail($id);

        $data = $this->validate([
            'ord|排序值' => 'require|integer',
        ]);

        $hotel->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "更新酒店排序值",
            $hotel->id,
            'hotel'
        );

        return $hotel;
    }

    /**
     * 禁用酒店
     */
    #[Put('hotel/:id/disable')]
    #[Pattern('id', '\d+')]
    public function disable($id)
    {
        $hotel = Hotel::findOrFail($id);

        // 设置状态为禁用
        $hotel->save(['status' => Hotel::STATUS_DISABLED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "禁用酒店",
            $hotel->id,
            'hotel'
        );

        return $hotel;
    }

    /**
     * 启用酒店
     */
    #[Put('hotel/:id/enable')]
    #[Pattern('id', '\d+')]
    public function enable($id)
    {
        $hotel = Hotel::findOrFail($id);

        // 设置状态为正常
        $hotel->save(['status' => Hotel::STATUS_NORMAL]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "启用酒店",
            $hotel->id,
            'hotel'
        );

        return $hotel;
    }

    /**
     * 取消审核，让酒店重新进入审核列表
     */
    #[Post('hotel/:id/unpend')]
    #[Pattern('id', '\d+')]
    public function unpend($id)
    {
        // 验证酒店是否存在且已审核通过（包括正常和禁用状态）
        $hotel = Hotel::whereIn('status', [Hotel::STATUS_NORMAL, Hotel::STATUS_DISABLED])->findOrFail($id);

        // 将酒店状态改为待审核
        $hotel->save(['status' => Hotel::STATUS_PENDING]);

        return $hotel;
    }

    /**
     * 设置酒店店主
     */
    #[Post('hotel/:id/owner')]
    #[Pattern('id', '\d+')]
    public function setOwner($id)
    {
        // 验证酒店是否存在
        $hotel = Hotel::findOrFail($id);

        // 验证提交的数据
        $data = $this->validate([
            'user_id|用户' => 'require',
        ]);

        // 查找用户
        $user = \app\model\User::findOrFail($data['user_id']);

        // 检查该用户是否已经是其他酒店的成员
        $isHotelMember = \app\model\HotelMember::where('user_id', $user->id)->find();

        if ($isHotelMember) {
            throw new ValidateException('该用户已是其他酒店的成员');
        }

        // 获取当前店主
        $currentOwner = $hotel->owner;

        // 使用事务处理
        $hotel->transaction(function () use ($hotel, $currentOwner, $user) {
            // 如果有当前店主，先移除
            if ($currentOwner) {
                $hotel->members()->detach($currentOwner->id);
            }

            // 设置新店主
            $hotel->members()->save($user, [
                'access_level' => \app\model\HotelMember::OWNER,
            ]);
        });

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "设置酒店店主",
            $hotel->id,
            'hotel'
        );

        return $hotel;
    }

    /**
     * 导出酒店数据
     */
    #[Post('hotel/export')]
    public function export()
    {
        // 获取所有酒店数据（不分页）
        $hotels = Hotel::order('id', 'desc')->select();

        // 检查是否有酒店数据
        if ($hotels->isEmpty()) {
            throw new ValidateException('没有酒店数据可导出');
        }

        // 生成文件名
        $baseFilename = '酒店数据_' . date('YmdHis');

        // 创建导出目录
        $disk = Filesystem::disk('uploads');
        $exportDir = 'exports';

        // 创建Excel文件
        $excelFilename = $baseFilename . '.xlsx';
        $excelPath = $exportDir . '/' . $excelFilename;
        $excelFullPath = root_path() . 'storage/uploads/' . $excelPath;

        // 确保目录存在
        $excelDir = dirname($excelFullPath);
        if (!is_dir($excelDir)) {
            mkdir($excelDir, 0755, true);
        }

        // 创建Spreadsheet对象
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('酒店数据');

        // 设置表头
        $headers = ['ID', '酒店名称', '酒店类型', '位置', '区县', '详细地址', '携程地址', '价格', '评分', '评语', '推荐值', '排序值'];
        $sheet->fromArray($headers, null, 'A1');

        // 设置表头样式
        $headerStyle = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'DDDDDD',
                ],
            ],
        ];

        $sheet->getStyle('A1:L1')->applyFromArray($headerStyle);

        // 准备数据
        $data = [];
        foreach ($hotels as $hotel) {
            // 从location中提取位置信息
            $locationName = $hotel->location ? ($hotel->location['name'] ?? '') : '';
            $district = $hotel->location ? ($hotel->location['district'] ?? '') : '';

            // 从rating中提取评分和评语
            $score = $hotel->rating ? ($hotel->rating['score'] ?? '') : '';
            $comment = $hotel->rating ? ($hotel->rating['comment'] ?? '') : '';

            $data[] = [
                $hotel->id,
                $hotel->name,
                $hotel->type,
                $locationName,
                $district,
                $hotel->address,
                $hotel->path,
                $hotel->price,
                $score,
                $comment,
                $hotel->rec,
                $hotel->ord
            ];
        }

        // 写入数据
        $sheet->fromArray($data, null, 'A2');

        // 设置价格列格式
        $sheet->getStyle('H2:H' . (count($data) + 1))->getNumberFormat()->setFormatCode('￥#,##0.00_-');

        // 自动调整列宽
        foreach (range('A', 'L') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // 创建Excel写入器
        $writer = new Xlsx($spreadsheet);

        // 使用内存流保存Excel文件
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);

        // 读取文件内容
        $fileContent = file_get_contents($tempFile);

        // 删除临时文件
        @unlink($tempFile);

        // 使用Filesystem保存文件
        $disk->write($excelPath, $fileContent);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_EXPORT,
            OperationLog::MODULE_HOTEL,
            "导出酒店数据",
            0,
            'hotel'
        );

        // 返回文件下载地址
        return json([
            'url' => '/uploads/' . $excelPath,
            'filename' => $excelFilename,
        ]);
    }

    /**
     * 验证酒店数据
     */
    protected function validateHotelData()
    {
        $data = $this->validate([
            'name|酒店名称' => 'require',
            'type|酒店类型' => 'require',
            'images|酒店图片' => 'require|array',
            'location|位置坐标' => 'require',
            'address|详细地址' => 'require',
            'path|携程地址' => 'string',
            'price|价格' => 'require|integer',
            'rating|评分' => 'array',
            'rec|推荐值' => 'integer',
            'ord|排序值' => 'integer',
        ]);

        $data['location']['district'] = get_district($data['location']['latitude'], $data['location']['longitude']);

        return $data;
    }
}
