<?php

namespace app\controller\admin;

use app\model\Coupon;
use app\model\Eatery;
use app\model\OperationLog;
use app\model\UserCoupon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use think\facade\Filesystem;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class CouponController extends Controller
{
    #[Get('coupon')]
    public function index()
    {
        // 获取优惠券列表
        $coupons = Coupon::order('id desc')->paginate();

        // 遍历每个优惠券，添加已核销数量字段
        $coupons->each(function ($coupon) {
            // 查询已核销的数量
            $used = UserCoupon::where('coupon_id', $coupon->id)
                ->whereNotNull('use_time')
                ->count();

            // 添加已核销数量字段
            $coupon->used = $used;
        });

        return $coupons;
    }

    #[Post('coupon')]
    public function save()
    {
        $data = $this->validate([
            'name|名称'           => 'require',
            'amount|金额'         => 'require|integer|min:0',
            'nums|数量'           => 'require|integer|min:0',
            'rules|使用规则'       => 'string',
            'start_time|开始时间' => 'date',
            'end_time|结束时间'   => 'date',
        ]);

        $data['status'] = 1;
        $coupon = Coupon::create($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_COUPON,
            "创建优惠券 {$data['name']}",
            $coupon->id,
            'coupon'
        );
    }

    #[Put('coupon/:id')]
    public function update($id)
    {
        $coupon = Coupon::findOrFail($id);

        $data = $this->validate([
            'name|名称'           => 'require',
            'amount|金额'         => 'require|integer|min:0',
            'nums|数量'           => 'require|integer|min:0',
            'rules|使用规则'       => 'string',
            'start_time|开始时间' => 'date',
            'end_time|结束时间'   => 'date',
        ]);

        $coupon->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_COUPON,
            "更新优惠券 {$data['name']}",
            $coupon->id,
            'coupon'
        );
    }

    /**
     * 获取指定消费券的核销记录
     */
    #[Get('coupon/:id/records')]
    #[Pattern('id', '\d+')]
    public function couponRecords($id)
    {
        // 验证消费券是否存在
        Coupon::findOrFail($id);

        // 获取指定消费券的核销记录
        $records = UserCoupon::with(['user', 'info', 'redeemable'])
            ->where('coupon_id', $id)
            ->whereNotNull('use_time')
            ->order('use_time', 'desc')
            ->paginate();

        // 处理商家信息（兼容旧数据）
        $records->each(function ($record) {
            // 优先使用多态关联
            if ($record->redeemable) {
                $record->merchant = $record->redeemable;
                $record->merchant_type = class_basename($record->redeemable_type);
            }
            // 兼容旧的eatery_id字段
            elseif ($record->eatery_id) {
                $record->merchant = Eatery::field(['id', 'name'])->find($record->eatery_id);
                $record->merchant_type = 'Eatery';
                // 保持向后兼容
                $record->eatery = $record->merchant;
            }
        });

        return $records;
    }

    /**
     * 导出指定消费券的核销记录
     */
    #[Post('coupon/:id/records/export')]
    #[Pattern('id', '\d+')]
    public function exportCouponRecords($id)
    {
        // 验证消费券是否存在
        $coupon = Coupon::findOrFail($id);

        // 获取指定消费券的所有核销记录（不分页）
        $records = UserCoupon::with(['user', 'info', 'redeemable'])
            ->where('coupon_id', $id)
            ->whereNotNull('use_time')
            ->order('use_time', 'desc')
            ->select();

        // 检查是否有核销记录
        if ($records->isEmpty()) {
            throw new ValidateException('没有核销记录可导出');
        }

        // 处理商家信息（兼容旧数据）
        $records->each(function ($record) {
            // 优先使用多态关联
            if ($record->redeemable) {
                $record->merchant = $record->redeemable;
                $record->merchant_type = class_basename($record->redeemable_type);
                $record->merchant_owner = $record->redeemable->owner;
                // 保持向后兼容
                $record->eatery = $record->merchant;
                $record->eatery_owner = $record->merchant_owner;
            }
            // 兼容旧的eatery_id字段
            elseif ($record->eatery_id) {
                $eatery = Eatery::find($record->eatery_id);
                $record->merchant = $eatery;
                $record->merchant_type = 'Eatery';
                $record->merchant_owner = $eatery->owner;
                // 保持向后兼容
                $record->eatery = $eatery;
                $record->eatery_owner = $eatery->owner;
            }
        });

        // 生成基础文件名（不含扩展名）
        $baseFilename = '消费券' . $coupon->name . '核销记录_' . date('YmdHis');

        // 创建导出目录
        $disk = Filesystem::disk('uploads');
        $exportDir = 'exports';

        // 准备详细记录数据
        $detailsData = $this->prepareDetailedRecordsData($records);

        // 准备商家汇总数据
        $summaryData = $this->prepareMerchantSummaryData($records);

        // 创建Excel文件
        $excelFilename = $baseFilename . '.xlsx';
        $excelPath = $exportDir . '/' . $excelFilename;
        $excelFullPath = root_path() . 'storage/uploads/' . $excelPath;

        // 确保目录存在
        $excelDir = dirname($excelFullPath);
        if (!is_dir($excelDir)) {
            mkdir($excelDir, 0755, true);
        }

        // 创建Spreadsheet对象
        $spreadsheet = new Spreadsheet();

        // 设置工作表属性
        $detailsSheetName = '详细记录';
        $summarySheetName = '商家汇总';

        // 设置第一个工作表（详细记录）
        $detailsSheet = $spreadsheet->getActiveSheet();
        $detailsSheet->setTitle($detailsSheetName);

        // 设置详细记录表头
        $detailsHeaders = ['用户', '手机号', '券码', '金额', '商家', '商家手机号', '核销时间'];
        $detailsSheet->fromArray($detailsHeaders, null, 'A1');

        // 设置表头样式
        $headerStyle = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'DDDDDD',
                ],
            ],
        ];

        $detailsSheet->getStyle('A1:G1')->applyFromArray($headerStyle);

        // 写入详细记录数据
        $detailsSheet->fromArray($detailsData, null, 'A2');

        // 设置金额列格式
        $detailsSheet->getStyle('D2:D' . (count($detailsData) + 1))->getNumberFormat()->setFormatCode('￥#,##0.00_-');

        // 设置时间列格式
        $detailsSheet->getStyle('G2:G' . (count($detailsData) + 1))->getNumberFormat()->setFormatCode('yyyy-mm-dd hh:mm:ss');

        // 自动调整列宽
        foreach (range('A', 'G') as $col) {
            $detailsSheet->getColumnDimension($col)->setAutoSize(true);
        }

        // 创建第二个工作表（商家汇总）
        $summarySheet = $spreadsheet->createSheet();
        $summarySheet->setTitle($summarySheetName);

        // 设置商家汇总表头
        $summaryHeaders = ['商家名称', '联系电话', '核销数量', '核销总金额(元)'];
        $summarySheet->fromArray($summaryHeaders, null, 'A1');

        // 应用表头样式
        $summarySheet->getStyle('A1:D1')->applyFromArray($headerStyle);

        // 写入商家汇总数据
        $summarySheet->fromArray($summaryData, null, 'A2');

        // 设置金额列格式
        $summarySheet->getStyle('D2:D' . (count($summaryData) + 1))->getNumberFormat()->setFormatCode("￥#,##0.00_-");

        // 自动调整列宽
        foreach (range('A', 'D') as $col) {
            $summarySheet->getColumnDimension($col)->setAutoSize(true);
        }

        // 返回到第一个工作表
        $spreadsheet->setActiveSheetIndex(0);

        // 创建Excel写入器
        $writer = new Xlsx($spreadsheet);

        // 使用内存流保存Excel文件
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);

        // 读取文件内容
        $fileContent = file_get_contents($tempFile);

        // 删除临时文件
        @unlink($tempFile);

        // 使用Filesystem保存文件
        $disk->write($excelPath, $fileContent);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_VERIFY,
            OperationLog::MODULE_COUPON,
            "导出优惠券核销记录",
            $coupon->id,
            'coupon'
        );

        // 返回文件下载地址
        return json([
            'url'      => '/uploads/' . $excelPath,
            'filename' => $excelFilename,
        ]);
    }

    /**
     * 准备详细记录数据
     *
     * @param array $records 核销记录数组
     * @return array 详细记录数据行
     */
    protected function prepareDetailedRecordsData($records)
    {
        $detailsData = [];

        // 添加详细记录数据行
        foreach ($records as $record) {
            $detailsData[] = [
                $record->user ? $record->user->nickname : '',
                $record->user ? $record->user->mobile : '',
                $record->code,
                $record->info ? $record->info->amount : 0,
                $record->eatery ? $record->eatery->name : '',
                $record->eatery_owner ? $record->eatery_owner->mobile : '',
                $record->use_time ? $record->use_time->format('Y-m-d H:i:s') : '',
            ];
        }

        return $detailsData;
    }

    /**
     * 准备商家汇总数据
     *
     * @param array $records 核销记录数组
     * @return array 商家汇总数据行
     */
    protected function prepareMerchantSummaryData($records)
    {
        // 按商家分组统计
        $merchantStats = [];

        foreach ($records as $record) {
            if (!$record->eatery_id) {
                continue; // 跳过没有商家信息的记录
            }

            $eateryId = $record->eatery_id;
            $amount = $record->info ? $record->info->amount : 0;

            if (!isset($merchantStats[$eateryId])) {
                $merchantStats[$eateryId] = [
                    'name' => $record->eatery ? $record->eatery->name : '未知商家',
                    'phone' => $record->eatery_owner ? $record->eatery_owner->mobile : '',
                    'count' => 0,
                    'total_amount' => 0,
                ];
            }

            $merchantStats[$eateryId]['count']++;
            $merchantStats[$eateryId]['total_amount'] += $amount;
        }

        // 准备商家汇总数据行
        $summaryData = [];

        // 添加商家汇总数据行
        foreach ($merchantStats as $stat) {
            $summaryData[] = [
                $stat['name'],
                $stat['phone'],
                $stat['count'],
                $stat['total_amount']
            ];
        }

        return $summaryData;
    }
}
