<?php

namespace app\controller\admin;

use app\model\Admin;
use app\model\OperationLog;
use Firebase\JWT\JWT;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\exception\ValidateException;
use yunwuxin\Auth;
use yunwuxin\auth\middleware\Authentication;

class AuthController extends Controller
{
    #[Get('auth/user')]
    #[Middleware(Authentication::class)]
    public function user(Auth $auth)
    {
        return $auth->user();
    }

    #[Post('auth/login')]
    public function login()
    {
        $data = $this->validate([
            'username|用户名' => 'require',
            'password|密码'   => 'require',
        ]);

        $admin = Admin::where('username', $data['username'])->find();

        if ($admin && password_verify($data['password'], $admin->password)) {
            // 记录登录日志
            OperationLog::record(
                $admin->id,
                OperationLog::USER_TYPE_ADMIN,
                OperationLog::ACTION_LOGIN,
                OperationLog::MODULE_ADMIN,
                "管理员登录"
            );

            return [
                'token' => JWT::encode([
                    'exp' => time() + 60 * 60 * 24 * 3,
                    'iat' => time(),
                    'id'  => $admin->id,
                ], config('app.token'), 'HS256'),
            ];
        }

        throw new ValidateException('用户名或密码不正确');
    }
}
