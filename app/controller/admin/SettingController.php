<?php

namespace app\controller\admin;

use app\model\OperationLog;
use app\model\Setting;
use think\annotation\route\Get;
use think\annotation\route\Put;

class SettingController extends Controller
{
    #[Get('setting/:name')]
    public function read($name)
    {
        $setting = Setting::read($name);

        return json($setting);
    }

    #[Put('setting/:name')]
    public function update($name)
    {
        $data = $this->request->getContent();
        $settingData = json_decode($data, true);
        Setting::write($name, $settingData);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SYSTEM,
            "更新系统设置 {$name}",
            0,
            'setting'
        );

        return json(['message' => '设置已更新']);
    }
}
