<?php

namespace app\controller\admin;

use app\BaseController;
use app\model\Admin;
use app\model\OperationLog;
use yunwuxin\Auth;

abstract class Controller extends BaseController
{
    /**
     * 当前登录的管理员
     *
     * @var Admin
     */
    protected $admin;

    /**
     * 初始化方法
     */
    protected function initialize()
    {
        parent::initialize();

        // 获取当前登录的管理员
        $this->admin = app(Auth::class)->user();
    }

    /**
     * 记录操作日志
     *
     * @param string $action 操作类型
     * @param string $module 操作模块
     * @param string $content 操作内容
     * @param int|null $targetId 操作对象ID
     * @param string|null $targetType 操作对象类型
     */
    protected function log($action, $module, $content, $targetId = null, $targetType = null)
    {
        OperationLog::record(
            $this->admin->id,
            OperationLog::USER_TYPE_ADMIN,
            $action,
            $module,
            $content,
            $targetId,
            $targetType
        );
    }
}
