<?php

namespace app\controller\admin;

use app\controller\api\Controller;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\Filesystem;
use yunwuxin\auth\middleware\Authentication;

class UploadController extends Controller
{
    #[Post('upload/:dir')]
    #[Middleware(Authentication::class)]
    public function index(Filesystem $filesystem)
    {
        $data = $this->validate([
            'dir'  => 'require|in:avatar,eatery,spot,hotel,setting',
            'file' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');

        $path = $disk->putFile($data['dir'], $data['file']);

        $result = [
            'url'   => $path,
            'value' => $path,
        ];

        return json($result);
    }
}
