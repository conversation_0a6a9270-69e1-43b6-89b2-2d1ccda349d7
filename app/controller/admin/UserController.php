<?php

namespace app\controller\admin;

use app\model\User;
use app\model\UserCoupon;
use think\annotation\route\Get;
use think\facade\Db;

class UserController extends Controller
{
    #[Get('user')]
    public function index()
    {
        // 获取用户列表
        $users = User::order('id desc')->paginate();

        // 遍历每个用户，添加已领取和已核销消费券数量
        $users->each(function ($user) {
            // 查询已领取的消费券数量
            $claimed = UserCoupon::where('user_id', $user->id)->count();

            // 查询已核销的消费券数量
            $used = UserCoupon::where('user_id', $user->id)
                ->whereNotNull('use_time')
                ->count();

            // 添加字段到用户对象
            $user->claimed_coupons = $claimed;
            $user->used_coupons    = $used;
        });

        return $users;
    }

    #[Get('user/search')]
    public function search()
    {
        $query = User::order('id desc');

        if ($this->request->has('query')) {
            $query->whereLike('nickname|mobile', "%{$this->request->get('query')}%");
        }

        return $query->limit(10)->select()->map(function (User $user) {
            return [
                'label' => "{$user->nickname}[{$user->mobile}]",
                'value' => $user->id,
            ];
        });
    }
}
