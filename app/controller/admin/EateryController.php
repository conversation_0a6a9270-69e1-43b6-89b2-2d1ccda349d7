<?php

namespace app\controller\admin;

use app\model\Eatery;
use app\model\EateryMember;
use app\model\HotelMember;
use app\model\OperationLog;
use app\model\Reservation;
use app\model\SpotMember;
use app\model\User;
use app\model\UserCoupon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use think\facade\Filesystem;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class EateryController extends Controller
{
    /**
     * 获取商家列表（已审核通过的商家，包括正常和禁用状态）
     */
    #[Get('eatery')]
    public function index()
    {
        $query = Eatery::whereIn('status', [Eatery::STATUS_APPROVED, Eatery::STATUS_DISABLED])
            ->append(['owner'])
            ->order('id', 'desc');

        $this->searchField($query, 'name');

        return $query->paginate();
    }

    /**
     * 获取待审核的商家列表
     */
    #[Get('eatery/pending')]
    public function pending()
    {
        return Eatery::where('status', Eatery::STATUS_PENDING)
            ->append(['owner'])
            ->order('id', 'desc')
            ->paginate();
    }

    /**
     * 获取商家详情
     */
    #[Get('eatery/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        // 获取商家信息，包括关联的用户信息
        $eatery = Eatery::with(['members'])->findOrFail($id);

        return $eatery;
    }

    /**
     * 审核商家（通过）
     */
    #[Post('eatery/:id/approve')]
    #[Pattern('id', '\d+')]
    public function approve($id)
    {
        $eatery = Eatery::where('status', Eatery::STATUS_PENDING)->findOrFail($id);

        $eatery->save(['status' => Eatery::STATUS_APPROVED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_APPROVE,
            OperationLog::MODULE_EATERY,
            "审核通过商家",
            $eatery->id,
            'eatery'
        );
    }

    /**
     * 审核商家（拒绝）
     */
    #[Post('eatery/:id/reject')]
    #[Pattern('id', '\d+')]
    public function reject($id)
    {
        $eatery = Eatery::where('status', Eatery::STATUS_PENDING)->findOrFail($id);

        $eatery->save(['status' => Eatery::STATUS_REJECTED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_REJECT,
            OperationLog::MODULE_EATERY,
            "拒绝商家审核",
            $eatery->id,
            'eatery'
        );
    }

    /**
     * 获取商家菜品列表
     */
    #[Get('eatery/:id/foods')]
    #[Pattern('id', '\d+')]
    public function foods($id)
    {
        // 验证商家是否存在并获取实例
        $eatery = Eatery::findOrFail($id);

        // 获取商家的所有菜品
        return $eatery->foods()->order('id', 'desc')->select();
    }

    /**
     * 获取商家消费券核销记录
     */
    #[Get('eatery/:id/coupons')]
    #[Pattern('id', '\d+')]
    public function coupons($id)
    {
        // 验证商家是否存在并获取实例
        $eatery = Eatery::findOrFail($id);

        // 使用关联方式获取商家的所有消费券核销记录
        return $eatery->coupons()
            ->with(['info', 'user'])
            ->whereNotNull('use_time')
            ->order('use_time', 'desc')
            ->paginate();
    }

    /**
     * 更新商家的推荐值
     */
    #[Put('eatery/:id/rec')]
    #[Pattern('id', '\d+')]
    public function updateRec($id)
    {
        // 验证商家是否存在
        $eatery = Eatery::findOrFail($id);

        // 验证提交的数据
        $data = $this->validate([
            'rec|推荐值' => 'require|integer',
        ]);

        // 更新商家信息
        $eatery->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_EATERY,
            "更新商家推荐值",
            $eatery->id,
            'eatery'
        );

        return $eatery;
    }

    /**
     * 更新商家的排序值
     */
    #[Put('eatery/:id/ord')]
    #[Pattern('id', '\d+')]
    public function updateOrd($id)
    {
        // 验证商家是否存在
        $eatery = Eatery::findOrFail($id);

        // 验证提交的数据
        $data = $this->validate([
            'ord|排序值' => 'require|integer',
        ]);

        // 更新商家信息
        $eatery->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_EATERY,
            "更新商家排序值",
            $eatery->id,
            'eatery'
        );

        return $eatery;
    }

    /**
     * 取消审核，让商家重新进入审核列表
     */
    #[Post('eatery/:id/unpend')]
    #[Pattern('id', '\d+')]
    public function unpend($id)
    {
        // 验证商家是否存在且已审核通过（包括正常和禁用状态）
        $eatery = Eatery::whereIn('status', [Eatery::STATUS_APPROVED, Eatery::STATUS_DISABLED])->findOrFail($id);

        // 将商家状态改为待审核
        $eatery->save(['status' => Eatery::STATUS_PENDING]);

        return $eatery;
    }

    /**
     * 获取餐厅的预订记录
     */
    #[Get('eatery/:id/reservations')]
    #[Pattern('id', '\d+')]
    public function reservations($id)
    {
        $query = Reservation::with(['user'])
            ->where('eatery_id', $id)
            ->order('create_time', 'desc');

        return $query->paginate();
    }

    /**
     * 获取餐厅的员工列表
     */
    #[Get('eatery/:id/members')]
    #[Pattern('id', '\d+')]
    public function members($id)
    {
        // 验证商家是否存在
        $eatery = Eatery::findOrFail($id);

        $members = $eatery->members()
            ->wherePivot('access_level', EateryMember::MEMBER)
            ->order('pivot.create_time', 'asc')
            ->select();

        return $members;
    }

    /**
     * 设置餐厅店主
     */
    #[Post('eatery/:id/owner')]
    #[Pattern('id', '\d+')]
    public function setOwner($id)
    {
        // 验证商家是否存在
        $eatery = Eatery::findOrFail($id);

        // 验证提交的数据
        $data = $this->validate([
            'user_id|用户' => 'require',
        ]);

        // 查找用户
        $user = User::findOrFail($data['user_id']);

        // 检查该用户是否已经是其他店铺的成员
        $isEateryMember = EateryMember::where('user_id', $user->id)->find();

        if ($isEateryMember) {
            throw new ValidateException('该用户已是其他商家的成员');
        }

        // 获取当前店主
        $currentOwner = $eatery->owner;

        // 使用事务处理
        $eatery->transaction(function () use ($eatery, $currentOwner, $user) {
            // 如果有当前店主，先移除
            if ($currentOwner) {
                $eatery->members()->detach($currentOwner->id);
            }

            // 设置新店主
            $eatery->members()->save($user, [
                'access_level' => EateryMember::OWNER,
            ]);
        });

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_EATERY,
            "设置商家店主为 {$user->nickname}",
            $eatery->id,
            'eatery'
        );
    }

    /**
     * 导出商家数据
     */
    #[Post('eatery/export')]
    public function export()
    {
        // 获取所有已审核通过的商家数据（不分页）
        $eateries = Eatery::where('status', Eatery::STATUS_APPROVED)
            ->order('id', 'desc')
            ->select();

        // 检查是否有商家数据
        if ($eateries->isEmpty()) {
            throw new ValidateException('没有商家数据可导出');
        }

        // 生成文件名
        $baseFilename = '商家数据_' . date('YmdHis');

        // 创建导出目录
        $disk      = Filesystem::disk('uploads');
        $exportDir = 'exports';

        // 创建Excel文件
        $excelFilename = $baseFilename . '.xlsx';
        $excelPath     = $exportDir . '/' . $excelFilename;
        $excelFullPath = root_path() . 'storage/uploads/' . $excelPath;

        // 确保目录存在
        $excelDir = dirname($excelFullPath);
        if (!is_dir($excelDir)) {
            mkdir($excelDir, 0755, true);
        }

        // 创建Spreadsheet对象
        $spreadsheet = new Spreadsheet();
        $sheet       = $spreadsheet->getActiveSheet();
        $sheet->setTitle('商家数据');

        // 设置表头
        $headers = ['ID', '商家名称', '商家类型', '营业时间', '人均消费', '包间预定', '区县', '店铺位置', '详细地址', '联系人', '联系电话', '标签', '收藏', '点赞', '浏览', '推荐值', '排序值'];
        $sheet->fromArray($headers, null, 'A1');

        // 设置表头样式
        $headerStyle = [
            'font'      => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
            'fill'      => [
                'fillType'   => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'DDDDDD',
                ],
            ],
        ];

        $sheet->getStyle('A1:Q1')->applyFromArray($headerStyle);

        // 准备数据
        $data = [];
        foreach ($eateries as $eatery) {
            // 从location中提取位置信息
            $locationName = $eatery->location ? ($eatery->location['name'] ?? '') : '';
            $district     = $eatery->location ? ($eatery->location['district'] ?? '') : '';

            // 格式化营业时间
            $hours = '';
            if ($eatery->hours) {
                $start = $eatery->hours['start'] ?? '';
                $end   = $eatery->hours['end'] ?? '';
                if ($start && $end) {
                    $hours = $start . '-' . $end;
                }
            }

            // 处理标签
            $tags = '';
            if ($eatery->tags && is_array($eatery->tags)) {
                $tags = implode(', ', $eatery->tags);
            }

            // 商家类型
            $type = $eatery->type == Eatery::TYPE_FOOD ? '美食' : '特产';

            // 包间预定
            $canReserve = $eatery->can_reserve ? '支持' : '不支持';

            // 获取联系人信息
            $contactName   = $eatery->owner ? $eatery->owner->nickname : '';
            $contactMobile = $eatery->owner ? $eatery->owner->mobile : '';

            $data[] = [
                $eatery->id,
                $eatery->name,
                $type,
                $hours,
                $eatery->cost,
                $canReserve,
                $district,
                $locationName,
                $eatery->address,
                $contactName,
                $contactMobile,
                $tags,
                $eatery->stars,
                $eatery->likes,
                $eatery->views,
                $eatery->rec,
                $eatery->ord,
            ];
        }

        // 写入数据
        $sheet->fromArray($data, null, 'A2');

        // 设置人均消费列格式
        $sheet->getStyle('E2:E' . (count($data) + 1))->getNumberFormat()->setFormatCode('¥#,##0.00_-');

        // 设置推荐值和排序值列格式，确保显示0值
        $sheet->getStyle('P2:Q' . (count($data) + 1))->getNumberFormat()->setFormatCode('0');

        // 自动调整列宽
        foreach (range('A', 'Q') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // 创建Excel写入器
        $writer = new Xlsx($spreadsheet);

        // 使用内存流保存Excel文件
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_');
        $writer->save($tempFile);

        // 读取文件内容
        $fileContent = file_get_contents($tempFile);

        // 删除临时文件
        @unlink($tempFile);

        // 使用Filesystem保存文件
        $disk->write($excelPath, $fileContent);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_EXPORT,
            OperationLog::MODULE_EATERY,
            "导出商家数据",
            0,
            'eatery'
        );

        // 返回文件下载地址
        return json([
            'url'      => '/uploads/' . $excelPath,
            'filename' => $excelFilename,
        ]);
    }
}
