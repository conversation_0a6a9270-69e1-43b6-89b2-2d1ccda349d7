<?php

namespace app\controller\admin;

use app\model\Admin;
use app\model\OperationLog;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class AdminController extends Controller
{
    /**
     * 获取管理员列表
     */
    #[Get('admin')]
    public function index()
    {
        return Admin::order('id desc')->paginate();
    }

    /**
     * 创建管理员
     */
    #[Post('admin')]
    public function save()
    {
        $data = $this->validate([
            'username|用户名'     => 'require|unique:admin',
            'password|密码'       => 'require|min:6',
            'access_level|权限级别' => 'integer',
        ]);

        // 对密码进行加密
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

        $admin = Admin::create($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_ADMIN,
            "创建管理员 {$data['username']}",
            $admin->id,
            'admin'
        );
    }

    /**
     * 获取管理员详情
     */
    #[Get('admin/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        return Admin::findOrFail($id);
    }

    /**
     * 更新管理员信息
     */
    #[Put('admin/:id')]
    #[Pattern('id', '\d+')]
    public function update($id)
    {
        $admin = Admin::findOrFail($id);

        $data = $this->validate([
            'username|用户名'     => 'require|unique:admin,username,' . $id,
            'access_level|权限级别' => 'integer',
        ]);

        $admin->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_ADMIN,
            "更新管理员 {$data['username']}",
            $admin->id,
            'admin'
        );
    }

    /**
     * 删除管理员
     */
    #[Delete('admin/:id')]
    #[Pattern('id', '\d+')]
    public function delete($id)
    {
        // 不允许删除ID为1的超级管理员
        if ($id == 1) {
            throw new ValidateException('不能删除超级管理员');
        }

        $admin = Admin::findOrFail($id);

        $admin->delete();

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_DELETE,
            OperationLog::MODULE_ADMIN,
            "删除管理员",
            $id,
            'admin'
        );
    }

    /**
     * 修改管理员密码
     */
    #[Post('admin/:id/password')]
    #[Pattern('id', '\d+')]
    public function updatePassword($id)
    {
        $admin = Admin::findOrFail($id);

        $data = $this->validate([
            'password|新密码' => 'require|min:6',
        ]);

        $admin->save([
            'password' => password_hash($data['password'], PASSWORD_DEFAULT)
        ]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_ADMIN,
            "修改管理员密码",
            $admin->id,
            'admin'
        );
    }
}
