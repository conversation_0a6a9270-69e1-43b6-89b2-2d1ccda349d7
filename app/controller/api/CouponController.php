<?php

namespace app\controller\api;

use app\model\Coupon;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

class CouponController extends Controller
{
    #[Get('coupon')]
    public function index()
    {
        $coupons = Coupon::available()->order('id desc')->select();

        return json($coupons);
    }

    #[Get('coupon/:id')]
    public function read($id)
    {
        $coupon = Coupon::available()->findOrFail($id);

        return json($coupon);
    }

    #[Post('coupon/:id/claim')]
    #[Middleware(Authentication::class)]
    public function claim($id)
    {
        $coupon = Coupon::available()->findOrFail($id);

        if ($coupon->users()->attached($this->user)) {
            throw new ValidateException('您已领取过该优惠券');
        }
        $coupon->transaction(function () use ($coupon) {
            $coupon->users()->attach($this->user);
            $coupon->inc('claimed')->save();
        });
    }

}
