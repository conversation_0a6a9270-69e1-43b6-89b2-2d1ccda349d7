<?php

namespace app\controller\api;

use app\lib\Agent;
use app\lib\Date;
use app\model\Conversation;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\Cache;
use yunwuxin\auth\middleware\Authentication;
use function think\swoole\helper\iterator;

#[Middleware(Authentication::class)]
class ChatController extends Controller
{
    #[Get('chat')]
    public function index()
    {
        $conversation = Conversation::recent()->where('user_id', $this->user->id)->with(['messages'])->find();

        return json([
            'conversation' => $conversation,
        ]);
    }

    #[Post('chat')]
    public function save(Agent $agent)
    {
        $data = $this->validate([
            'query'        => 'require',
            'conversation' => '',
        ]);

        if (empty($data['conversation'])) {
            $conversation = Conversation::recent()
                ->where('user_id', $this->user->id)
                ->find();
        } else {
            $conversation = Conversation::where('id', $data['conversation'])
                ->where('user_id', $this->user->id)
                ->find();
        }

        if (empty($conversation)) {
            $conversation = Conversation::create([
                'user_id' => $this->user->id,
            ]);
        } else {
            $conversation->save(['update_time' => Date::now()]);
        }

        $messages = $conversation->messages()
            ->limit(5)
            ->order('create_time desc')
            ->select();

        $historyMessages = [];

        foreach ($messages as $message) {
            $historyMessages[] = [
                'role'    => 'user',
                'content' => $message->query,
            ];

            $historyMessages[] = [
                'role'    => 'assistant',
                'content' => $message->content,
            ];
        }

        $result = $agent->chat([
            [
                'role'    => 'system',
                'content' => <<<EOT
# 角色：
吴忠资深旅游管家，专注于提供吴忠旅游相关的信息和咨询服务。

# 工作任务：
- 回答用户关于吴忠旅游的问题，包括景点推荐、美食推荐、住宿推荐、交通指南等。
- 提供吴忠旅游的最新动态和相关信息。
- 解答用户在吴忠旅游过程中可能遇到的疑问和问题。

# 输出示例：
- 用户：吴忠有哪些值得一去的景点？
  - 吴忠资深旅游管家：吴忠有许多美丽的景点，比如青铜峡黄河大峡谷、吴忠黄河楼、同心清真大寺等，每个景点都有其独特的魅力和历史文化底蕴，非常值得一游。
- 用户：吴忠的美食有哪些推荐？
  - 吴忠资深旅游管家：吴忠的美食以清真食品为主，其中吴忠手抓羊肉、羊肉泡馍、烩小吃等都是当地的特色美食，绝对值得一试。
- 用户：吴忠的住宿条件怎么样？
  - 吴忠资深旅游管家：吴忠的住宿条件比较成熟，从高档酒店到经济型酒店都有，可以根据自己的需求和预算进行选择。

# 注意事项：
- 只回答关于吴忠旅游的问题，不回答其他问题。
EOT
                ,

            ],
            ...$historyMessages,
            [
                'role'    => 'user',
                'content' => $data['query'],
            ],
        ]);

        $generator = function () use ($data, $conversation, $result) {
            $content = "";
            foreach ($result as $event) {
                $content .= $event;
                yield 'data: ' . json_encode($event) . "\n\n";
            }

            yield "data: [DONE]\n\n";

            $conversation->messages()->save([
                'query'   => $data['query'],
                'content' => $content,
            ]);
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    #[Get('chat/questions')]
    public function questions(Cache $cache)
    {
        $questions = $cache->remember('questions', function (Agent $agent) {
            $messages = [
                [
                    'role'    => 'system',
                    'content' => <<<EOT
# 角色设定
- **身份**：吴忠文旅局首席体验官，具备10年在地旅游服务经验

# 工作流程
1. **拆解旅游要素**：首先，详细拆解吴忠的「吃住行游购娱」六大旅游要素。
2. **分析游客需求**：其次，深入分析不同游客画像（亲子、老年、自驾、摄影等）的核心诉求。
3. **匹配季节与政策**：最后，考虑季节性和政策变化因素，如节假日、特殊活动等。

# 输出要求
- **内容**：生成15-20个游客高频咨询问题，覆盖景点选择、美食地图、交通指南、文化体验、季节特色、行程规划六大维度。
- **特殊群体需求**：问题中需包含亲子、老年、自驾、摄影等特殊群体的需求。
- **地域独特性**：突出红色旅游、黄河文化、回族风情等吴忠地域独特性标签。
- 请以标准的JSON格式输出，可以直接被json_decode解析，不需要使用代码标签包裹

# 输出示例
[
  "吴忠哪里比较好玩",
  "吴忠市游玩攻略",
  "推荐一下吴忠美食"
]
EOT
                    ,

                ],
            ];

            $res = $agent->chat($messages, false);

            $result = $res->getReturn();

            return json_decode($result, true);
        }, 60 * 60 * 24);

        // 随机获取4条问题
        if (is_array($questions) && count($questions) > 4) {
            // 打乱数组并取前4条
            shuffle($questions);
            $questions = array_slice($questions, 0, 4);
        }

        return json($questions);
    }
}
