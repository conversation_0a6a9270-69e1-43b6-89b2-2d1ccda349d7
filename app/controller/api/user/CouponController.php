<?php

namespace app\controller\api\user;

use app\controller\api\Controller;
use app\lib\Date;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class CouponController extends Controller
{
    #[Get('user/coupon')]
    public function index()
    {
        $query = $this->user->coupons();

        $this->filterFields($query, [
            'status' => function ($query, $value) {
                switch ($value) {
                    case 1:
                        $query->available()->wherePivot('use_time', null);
                        break;
                    case 2:
                        $query->wherePivot('use_time', '<>', null);
                        break;
                    case 3:
                        $query->wherePivot('use_time', null)->where('end_time', '<', Date::now()->startOfDay());
                        break;
                }
            },
        ]);

        return json($query->paginate());
    }

    #[Get('user/coupon/valid')]
    public function valid()
    {
        $coupon = $this->user->coupons()->available()->wherePivot('use_time', null)->find();

        return json($coupon);
    }

    #[Get('user/coupon/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        /** @var \app\model\Coupon $coupon */
        $coupon = $this->user->coupons()->wherePivot('id', $id)->findOrFail();

        $writer = new PngWriter();
        // Create QR code
        $qrCode = new QrCode(
            data: $coupon->pivot->code,
            size: 400,
            margin: 30,
        );

        $result = $writer->write($qrCode);

        $coupon->qrcode = $result->getDataUri();

        return json($coupon);
    }
}
