<?php

namespace app\controller\api\user;

use app\controller\api\Controller;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class IndexController extends Controller
{
    #[Get('user/eatery')]
    public function eatery()
    {
        /** @var \app\model\Eatery $eatery */
        $eatery = $this->user->eateries()->find();

        return json($eatery);
    }

    #[Get('user/star/eatery')]
    public function starEatery()
    {
        return json($this->user->starEateries()->paginate());
    }

    #[Get('user/star/spot')]
    public function starSpot()
    {
        return json($this->user->starSpots()->paginate());
    }

    #[Get('user/spot')]
    public function spot()
    {
        /** @var \app\model\Spot $spot */
        $spot = $this->user->spots()->find();

        return json($spot);
    }

    #[Get('user/hotel')]
    public function hotel()
    {
        /** @var \app\model\Hotel $hotel */
        $hotel = $this->user->hotels()->find();

        return json($hotel);
    }
}
