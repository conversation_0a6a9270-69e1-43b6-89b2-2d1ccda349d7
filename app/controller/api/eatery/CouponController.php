<?php

namespace app\controller\api\eatery;

use app\controller\api\Controller;
use app\lib\Date;
use app\lib\Hashids;
use app\model\UserCoupon;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\exception\ValidateException;

class CouponController extends Controller
{
    use WithEatery;

    #[Get('eatery/coupon')]
    public function index()
    {
        $coupons = $this->eatery->coupons()
            ->with('info')
            ->order('use_time', 'desc')
            ->paginate();

        return json($coupons);
    }

    #[Get('eatery/coupon/amount')]
    public function amount()
    {
        $amount = $this->eatery->coupons()
            ->with('info')
            ->whereBetween('use_time', [Date::now()->startOfMonth(), Date::now()->endOfMonth()])
            ->select()
            ->reduce(function ($pre, $item) {
                return $pre + $item->info->amount;
            }, 0);

        return json(['amount' => $amount]);
    }

    #[Get('eatery/coupon/{code}')]
    public function read($code)
    {
        $id     = Hashids::decode($code);
        $coupon = UserCoupon::with('info')->whereNull('use_time')->find($id);
        if (!$coupon) {
            throw new ValidateException('优惠券不存在或已使用');
        }
        if (!$coupon->isAvailable()) {
            throw new ValidateException('优惠券已过期');
        }

        return json($coupon);
    }

    #[Post('eatery/coupon/{id}/redeem')]
    public function redeem($id)
    {
        $coupon = UserCoupon::with('info')->whereNull('use_time')->find($id);

        if (!$coupon) {
            throw new ValidateException('优惠券不存在或已使用');
        }
        if (!$coupon->isAvailable()) {
            throw new ValidateException('优惠券已过期');
        }

        $coupon->save([
            'redeemable_type' => 'app\\model\\Eatery',
            'redeemable_id'   => $this->eatery->id,
            'use_time'        => Date::now(),
        ]);
    }
}
