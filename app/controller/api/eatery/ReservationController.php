<?php

namespace app\controller\api\eatery;

use app\controller\api\Controller;
use app\model\OperationLog;
use app\model\Reservation;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class ReservationController extends Controller
{
    use WithEatery;

    /**
     * 获取餐厅的预订列表
     *
     * @param int|null $status 预订状态：0=待确认，1=已确认，2=已取消，不传则获取所有状态
     */
    #[Get('eatery/reservation')]
    public function index($status = null)
    {
        // 获取业务日期的开始时间（考虑凌晨0-3点的情况）
        $startTime = Reservation::getBusinessDayStart();
        // 明天凌晨3点结束
        $endTime = $startTime->copy()->addDay();

        $query = $this->eatery->reservations()
            ->with(['user'])
            ->where('create_time', '>=', $startTime)
            ->where('create_time', '<=', $endTime);

        // 根据状态筛选
        if ($status !== null) {
            $query->where('status', $status);
        }

        $query->order('create_time', 'desc');

        $reservations = $query->select();

        return json($reservations);
    }

    /**
     * 确认预订
     */
    #[Post('eatery/reservation/:id/confirm')]
    #[Pattern('id', '\d+')]
    public function confirm($id)
    {
        // 获取预订记录
        /** @var \app\model\Reservation $reservation */
        $reservation = $this->eatery->reservations()->findOrFail($id);

        // 只能确认当天的待确认预订
        if (!$reservation->isValid() || $reservation->status !== Reservation::STATUS_PENDING) {
            throw new ValidateException('只能确认当天的待确认预订');
        }

        // 更新预订状态为已确认
        $reservation->save(['status' => Reservation::STATUS_CONFIRMED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_APPROVE,
            OperationLog::MODULE_RESERVATION,
            "确认预订",
            $reservation->id,
            'reservation'
        );

        return json(['message' => '预订已确认']);
    }

    /**
     * 取消预订
     */
    #[Post('eatery/reservation/:id/cancel')]
    #[Pattern('id', '\d+')]
    public function cancel($id)
    {
        // 获取预订记录
        /** @var \app\model\Reservation $reservation */
        $reservation = $this->eatery->reservations()->findOrFail($id);

        // 只能取消当天的有效预订
        if (!$reservation->isValid()) {
            throw new ValidateException('只能取消当天的有效预订');
        }

        // 更新预订状态为已取消
        $reservation->save(['status' => Reservation::STATUS_CANCELLED]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_REJECT,
            OperationLog::MODULE_RESERVATION,
            "取消预订",
            $reservation->id,
            'reservation'
        );

        return json(['message' => '预订已取消']);
    }

}
