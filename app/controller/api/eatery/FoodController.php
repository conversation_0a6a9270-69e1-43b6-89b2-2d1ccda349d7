<?php

namespace app\controller\api\eatery;

use app\controller\api\Controller;
use app\model\OperationLog;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;

class FoodController extends Controller
{
    use WithEatery;

    #[Get('eatery/food')]
    public function index()
    {
        return $this->eatery->foods()->select();
    }

    #[Post('eatery/food')]
    public function save()
    {
        $data = $this->validate([
            'name|商品名称'  => 'require',
            'price|商品价格' => 'require',
            'cover|商品图片' => 'require',
        ]);

        /** @var \app\model\Food $food */
        $food = $this->eatery->foods()->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_FOOD,
            "创建菜品 {$data['name']}",
            $food->id,
            'food'
        );
    }

    #[Get('eatery/food/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        // 获取指定 ID 的食物
        $food = $this->eatery->foods()->findOrFail($id);

        return json($food);
    }

    #[Put('eatery/food/:id')]
    #[Pattern('id', '\d+')]
    public function update($id)
    {
        // 先获取指定 ID 的食物，确保资源存在
        /** @var \app\model\Food $food */
        $food = $this->eatery->foods()->findOrFail($id);

        // 然后验证提交的数据
        $data = $this->validate([
            'name|商品名称'  => 'require',
            'price|商品价格' => 'require',
            'cover|商品图片' => 'require',
        ]);

        // 更新食物信息
        $food->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_FOOD,
            "更新菜品 {$food->name}",
            $food->id,
            'food'
        );

        return json($food);
    }

    #[Delete('eatery/food/:id')]
    public function delete($id)
    {
        /** @var \app\model\Food $food */
        $food = $this->eatery->foods()->findOrFail($id);

        $food->delete();

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_DELETE,
            OperationLog::MODULE_FOOD,
            "删除菜品 {$food->name}",
            $id,
            'food'
        );
    }
}
