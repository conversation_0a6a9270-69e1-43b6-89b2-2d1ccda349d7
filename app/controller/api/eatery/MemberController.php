<?php

namespace app\controller\api\eatery;

use app\controller\api\Controller;
use app\model\EateryMember;
use app\model\OperationLog;
use app\model\User;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\exception\ValidateException;

class MemberController extends Controller
{
    use WithEatery;

    #[Get('eatery/member')]
    public function index()
    {
        return $this->eatery->members()->wherePivot('access_level', EateryMember::MEMBER)->select();
    }

    #[Post('eatery/member')]
    public function save()
    {
        $data = $this->validate([
            'mobile' => 'require',
        ]);

        $user = User::where('mobile', $data['mobile'])->find();
        if (!$user) {
            throw new ValidateException('用户不存在');
        }

        if (EateryMember::where('user_id', $user->id)->find()) {
            throw new ValidateException('该用户已是商户成员');
        }

        $this->eatery->members()->save($user, [
            'access_level' => EateryMember::MEMBER,
        ]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_EATERY,
            "添加商户成员 {$user->nickname}",
            $user->id,
            'user'
        );
    }

    #[Delete('eatery/member/:id')]
    public function delete($id)
    {
        // 获取用户信息，用于日志记录
        $user = User::find($id);

        $this->eatery->members()->detach($id);

        // 记录操作日志
        if ($user) {
            $this->log(
                OperationLog::ACTION_DELETE,
                OperationLog::MODULE_EATERY,
                "删除商户成员 {$user->nickname}",
                $user->id,
                'user'
            );
        }
    }
}
