<?php

namespace app\controller\api\eatery;

use app\controller\api\Controller;
use app\model\Eatery;
use app\model\EateryMember;
use app\model\OperationLog;
use app\model\Reservation;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\db\Query;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

class IndexController extends Controller
{
    #[Get('eatery')]
    public function index()
    {
        $query = Eatery::where('status', 1);

        $sort = $this->request->param('sort');
        if ($sort) {
            $query->order($sort, 'desc');
        } else {
            $query->order('rec', 'desc')
                ->order('ord', 'desc')
                ->orderRaw('(stars * 100 + likes * 10 + views) DESC')
                ->order('id', 'desc');
        }

        $this->filterFields($query, [
            'type',
            'district' => function (Query $query, $value) {
                $query->where('location->district', $value);
            }
        ]);

        $eateries = $query->paginate();

        return json($eateries);
    }

    #[Get('eatery/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        $eatery = Eatery::with(['foods'])->findOrFail($id);

        $eatery->inc('views')->save();

        if ($this->user) {
            $eatery->liked = !!$eatery->likes()->attached($this->user);
            $eatery->starred = !!$eatery->stars()->attached($this->user);
            $eatery->reserved = Reservation::isReserved($eatery->id, $this->user->id);
        } else {
            $eatery->liked = false;
            $eatery->starred = false;
            // 未登录用户，如果餐厅支持预订则设置为0（未预订），否则设置为-1（不支持预订）
            $eatery->reserved = ($eatery->status == Eatery::STATUS_APPROVED && $eatery->can_reserve == 1) ? 0 : -1;
        }

        return json($eatery);
    }

    #[Post('eatery')]
    #[Middleware(Authentication::class)]
    public function save()
    {
        $eatery = $this->user->eateries()->find();
        if ($eatery) {
            throw new ValidateException('您已入驻过商家');
        }

        $data = $this->validateEateryData();

        $eatery = $this->user->eateries()->save($data, [
            'access_level' => EateryMember::OWNER,
        ]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_EATERY,
            "创建商家 {$data['name']}",
            $eatery->id,
            'eatery'
        );
    }

    #[Put('eatery')]
    #[Middleware(Authentication::class)]
    public function update()
    {
        // 使用关联方法查找当前用户的餐厅
        /** @var Eatery $eatery */
        $eatery = $this->user->eateries()->find();
        if (!$eatery) {
            throw new ValidateException('您还没有餐厅');
        }

        // 验证提交的数据
        $data = $this->validateEateryData();

        if ($eatery->status == 2) {
            $data['status'] = 0;
        }

        // 更新餐厅信息
        $eatery->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_EATERY,
            "更新商家 {$eatery->name} 的信息",
            $eatery->id,
            'eatery'
        );

        return json($eatery);
    }

    #[Post('eatery/:id/like')]
    #[Middleware(Authentication::class)]
    public function like($id)
    {
        $eatery = Eatery::findOrFail($id);

        if ($eatery->likes()->attached($this->user)) {
            $eatery->likes()->detach($this->user);
            $eatery->dec('likes')->save();
        } else {
            $eatery->likes()->attach($this->user);
            $eatery->inc('likes')->save();
        }
    }

    #[Post('eatery/:id/star')]
    #[Middleware(Authentication::class)]
    public function star($id)
    {
        $eatery = Eatery::findOrFail($id);

        if ($eatery->stars()->attached($this->user)) {
            $eatery->stars()->detach($this->user);
            $eatery->dec('stars')->save();
        } else {
            $eatery->stars()->attach($this->user);
            $eatery->inc('stars')->save();
        }
    }

    /**
     * 预定或取消预定餐厅
     */
    #[Post('eatery/:id/reserve')]
    #[Middleware(Authentication::class)]
    public function reserve($id)
    {
        // 检查餐厅是否存在
        $eatery = Eatery::findOrFail($id);

        // 获取业务日期的开始时间（考虑凌晨0-3点的情况）
        $today = Reservation::getBusinessDayStart();
        // 明天凌晨3点结束
        $tomorrow = $today->copy()->addDay();

        $reservation = Reservation::where('eatery_id', $eatery->id)
            ->where('user_id', $this->user->id)
            ->where('create_time', '>=', $today)
            ->where('create_time', '<', $tomorrow)
            ->whereIn('status', [Reservation::STATUS_PENDING, Reservation::STATUS_CONFIRMED])
            ->find();

        // 如果已经预订，则取消预订
        if ($reservation) {
            // 只能取消当天的有效预订
            if (!$reservation->isValid()) {
                throw new ValidateException('只能取消当天的有效预订');
            }

            // 只能取消确认中的预订
            if ($reservation->status !== Reservation::STATUS_PENDING) {
                throw new ValidateException('只能取消确认中的预订');
            }

            // 删除预订记录
            $reservation->delete();
        } else {
            // 如果未预订，则创建预订（需检查餐厅是否支持预订）
            if ($eatery->status != Eatery::STATUS_APPROVED || $eatery->can_reserve != 1) {
                throw new ValidateException('该餐厅不支持预订');
            }

            // 验证预订时间和人数
            $data = $this->validate([
                'reservation_time|预订时间' => '',
                'people_count|预订人数' => 'number|min:1',
            ]);

            // 创建预订记录
            Reservation::create([
                'eatery_id' => $eatery->id,
                'user_id' => $this->user->id,
                'status' => Reservation::STATUS_PENDING,
                'reservation_time' => $data['reservation_time'] ?? null,
                'people_count' => $data['people_count'] ?? null,
            ]);
        }
    }

    /**
     * 验证餐厅数据
     * @return array 验证后的数据
     */
    private function validateEateryData()
    {
        return $this->validate([
            'images|店铺图片' => 'require',
            'name|店铺名称' => 'require',
            'hours|营业时间' => function ($value) {
                if (empty($value['start']) || empty($value['end'])) {
                    return '营业时间不能为空';
                }
                if ($value['start'] > $value['end']) {
                    return '营业开始时间不能小于结束时间';
                }
                return true;
            },
            'cost|人均消费' => '',
            'location|店铺位置' => 'require',
            'address|详细地址' => 'require',
            'phone|联系方式' => '',
            'type|商铺类型' => 'require|in:' . Eatery::TYPE_FOOD . ',' . Eatery::TYPE_SPECIALTY,
            'can_reserve|是否支持预订' => 'in:0,1',
            'tags|标签' => '',
        ]);
    }
}
