<?php

namespace app\controller\api\spot;

use app\controller\api\Controller;
use app\model\Spot;
use app\model\SpotMember;
use app\model\OperationLog;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

class IndexController extends Controller
{
    #[Get('spot')]
    public function index()
    {
        $query = Spot::where('status', Spot::STATUS_NORMAL)
            ->where('rec', 0); // 只显示非推荐的景点

        $sort = $this->request->param('sort');
        if ($sort) {
            $query->order($sort, 'desc');
        } else {
            $query->order('ord', 'desc')
                ->orderRaw('(stars * 100 + likes * 10 + views) DESC')
                ->order('id', 'desc');
        }

        $this->filterFields($query, [
            'district' => function ($query, $value) {
                $query->where('location->district', $value);
            },
        ]);

        $spots = $query->paginate();

        return json($spots);
    }

    /**
     * 获取推荐景点列表
     */
    #[Get('spot/recommend')]
    public function recommend()
    {
        $query = Spot::where('status', Spot::STATUS_NORMAL)
            ->where('rec', '>', 0); // 只显示推荐的景点

        $sort = $this->request->param('sort');
        if ($sort) {
            $query->order($sort, 'desc');
        } else {
            $query->order('rec', 'desc')
                ->order('ord', 'desc')
                ->orderRaw('(stars * 100 + likes * 10 + views) DESC')
                ->order('id', 'desc');
        }

        $this->filterFields($query, [
            'district' => function ($query, $value) {
                $query->where('location->district', $value);
            },
        ]);

        $spots = $query->select();

        return json($spots);
    }

    /**
     * 获取景点详情
     */
    #[Get('spot/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        $spot = Spot::findOrFail($id);

        $spot->inc('views')->save();

        if ($this->user) {
            $spot->liked   = !!$spot->likeUsers()->attached($this->user);
            $spot->starred = !!$spot->starUsers()->attached($this->user);
        } else {
            $spot->liked   = false;
            $spot->starred = false;
        }

        return json($spot);
    }

    /**
     * 点赞景点
     */
    #[Post('spot/:id/like')]
    #[Middleware(Authentication::class)]
    #[Pattern('id', '\d+')]
    public function like($id)
    {
        $spot = Spot::findOrFail($id);

        if ($spot->likeUsers()->attached($this->user)) {
            $spot->likeUsers()->detach($this->user);
            $spot->dec('likes')->save();
        } else {
            $spot->likeUsers()->attach($this->user);
            $spot->inc('likes')->save();
        }
    }

    /**
     * 收藏景点
     */
    #[Post('spot/:id/star')]
    #[Middleware(Authentication::class)]
    #[Pattern('id', '\d+')]
    public function star($id)
    {
        $spot = Spot::findOrFail($id);

        if ($spot->starUsers()->attached($this->user)) {
            $spot->starUsers()->detach($this->user);
            $spot->dec('stars')->save();
        } else {
            $spot->starUsers()->attach($this->user);
            $spot->inc('stars')->save();
        }
    }

    /**
     * 景点入驻申请
     */
    #[Post('spot')]
    #[Middleware(Authentication::class)]
    public function save()
    {
        $spot = $this->user->spots()->find();
        if ($spot) {
            throw new ValidateException('您已入驻过景点');
        }

        $data = $this->validateSpotData();

        $spot = $this->user->spots()->save($data, [
            'access_level' => SpotMember::OWNER,
        ]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_SPOT,
            "创建景点 {$data['name']}",
            $spot->id,
            'spot'
        );

        return json($spot);
    }

    /**
     * 更新景点信息
     */
    #[Put('spot')]
    #[Middleware(Authentication::class)]
    public function update()
    {
        /** @var Spot $spot */
        $spot = $this->user->spots()->find();
        if (!$spot) {
            throw new ValidateException('您还没有景点');
        }

        $data = $this->validateSpotData();

        if ($spot->status == Spot::STATUS_REJECTED) {
            $data['status'] = Spot::STATUS_PENDING;
        }

        $spot->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SPOT,
            "更新景点 {$spot->name} 的信息",
            $spot->id,
            'spot'
        );

        return json($spot);
    }

    /**
     * 验证景点数据
     * @return array 验证后的数据
     */
    private function validateSpotData()
    {
        $data = $this->validate([
            'images|景点图片'   => 'require',
            'name|景点名称'     => 'require',
            'hours|开放时间'    => function ($value) {
                if (empty($value['start']) || empty($value['end'])) {
                    return '开放时间不能为空';
                }
                if ($value['start'] > $value['end']) {
                    return '开放开始时间不能小于结束时间';
                }
                return true;
            },
            'location|景点位置' => 'require',
            'address|详细地址'  => 'require',
            'phone|联系方式'    => '',
            'tickets|门票信息'  => '',
        ]);

        // 添加地区信息
        $data['location']['district'] = get_district($data['location']['latitude'], $data['location']['longitude']);

        return $data;
    }
}
