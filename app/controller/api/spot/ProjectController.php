<?php

namespace app\controller\api\spot;

use app\controller\api\Controller;
use app\model\OperationLog;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Put;

class ProjectController extends Controller
{
    use WithSpot;

    #[Get('spot/project')]
    public function index()
    {
        return $this->spot->projects()->select();
    }

    #[Post('spot/project')]
    public function save()
    {
        $data = $this->validate([
            'name|项目名称'  => 'require',
            'price|项目价格' => 'require',
        ]);

        /** @var \app\model\SpotProject $project */
        $project = $this->spot->projects()->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_SPOT,
            "创建景点项目 {$data['name']}",
            $project->id,
            'spot_project'
        );
    }

    #[Get('spot/project/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        // 获取指定 ID 的项目
        $project = $this->spot->projects()->findOrFail($id);

        return json($project);
    }

    #[Put('spot/project/:id')]
    #[Pattern('id', '\d+')]
    public function update($id)
    {
        // 先获取指定 ID 的项目，确保资源存在
        /** @var \app\model\SpotProject $project */
        $project = $this->spot->projects()->findOrFail($id);

        // 然后验证提交的数据
        $data = $this->validate([
            'name|项目名称'  => 'require',
            'price|项目价格' => 'require',
        ]);

        // 更新项目信息
        $project->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_SPOT,
            "更新景点项目 {$project->name}",
            $project->id,
            'spot_project'
        );

        return json($project);
    }

    #[Delete('spot/project/:id')]
    public function delete($id)
    {
        /** @var \app\model\SpotProject $project */
        $project = $this->spot->projects()->findOrFail($id);

        $project->delete();

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_DELETE,
            OperationLog::MODULE_SPOT,
            "删除景点项目 {$project->name}",
            $id,
            'spot_project'
        );
    }
}
