<?php

namespace app\controller\api\spot;

use app\controller\api\Controller;
use app\lib\Date;
use app\lib\Hashids;
use app\model\UserCoupon;
use app\controller\api\spot\WithSpot;
use think\exception\ValidateException;
use think\annotation\route\Get;
use think\annotation\route\Post;

class CouponController extends Controller
{
    use WithSpot;

    #[Get('spot/coupon')]
    public function index()
    {
        $coupons = $this->spot->coupons()
            ->with('info')
            ->order('use_time', 'desc')
            ->paginate();

        return json($coupons);
    }

    #[Get('spot/coupon/amount')]
    public function amount()
    {
        $amount = $this->spot->coupons()
            ->with('info')
            ->whereBetween('use_time', [Date::now()->startOfMonth(), Date::now()->endOfMonth()])
            ->select()
            ->reduce(function ($pre, $item) {
                return $pre + $item->info->amount;
            }, 0);

        return json(['amount' => $amount]);
    }

    #[Get('spot/coupon/{code}')]
    public function read($code)
    {
        $id     = Hashids::decode($code);
        $coupon = UserCoupon::with('info')->whereNull('use_time')->find($id);
        if (!$coupon) {
            throw new ValidateException('优惠券不存在或已使用');
        }
        if (!$coupon->isAvailable()) {
            throw new ValidateException('优惠券已过期');
        }

        return json($coupon);
    }

    #[Post('spot/coupon/{id}/redeem')]
    public function redeem($id)
    {
        $coupon = UserCoupon::with('info')->whereNull('use_time')->find($id);

        if (!$coupon) {
            throw new ValidateException('优惠券不存在或已使用');
        }
        if (!$coupon->isAvailable()) {
            throw new ValidateException('优惠券已过期');
        }

        $coupon->save([
            'redeemable_type' => 'app\\model\\Spot',
            'redeemable_id'   => $this->spot->id,
            'use_time'        => Date::now(),
        ]);
    }
}
