<?php

namespace app\controller\api\spot;

use app\controller\api\Controller;
use app\model\SpotMember;
use app\model\User;
use app\model\OperationLog;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Delete;
use think\annotation\route\Middleware;
use yunwuxin\auth\middleware\Authentication;
use think\exception\ValidateException;

#[Middleware(Authentication::class)]
class MemberController extends Controller
{
    use WithSpot;

    #[Get('spot/member')]
    public function index()
    {
        return $this->spot->members()->wherePivot('access_level', SpotMember::MEMBER)->select();
    }

    #[Post('spot/member')]
    public function save()
    {
        $data = $this->validate([
            'mobile' => 'require',
        ]);

        $user = User::where('mobile', $data['mobile'])->find();
        if (!$user) {
            throw new ValidateException('用户不存在');
        }

        if (SpotMember::where('user_id', $user->id)->find()) {
            throw new ValidateException('该用户已是景点成员');
        }

        $this->spot->members()->save($user, [
            'access_level' => SpotMember::MEMBER,
        ]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_SPOT,
            "添加景点成员 {$user->nickname}",
            $user->id,
            'user'
        );

        return json(['message' => '添加成员成功']);
    }

    #[Delete('spot/member/:id')]
    public function delete($id)
    {
        // 获取用户信息，用于日志记录
        $user = User::find($id);

        $this->spot->members()->detach($id);

        // 记录操作日志
        if ($user) {
            $this->log(
                OperationLog::ACTION_DELETE,
                OperationLog::MODULE_SPOT,
                "删除景点成员 {$user->nickname}",
                $user->id,
                'user'
            );
        }

        return json(['message' => '删除成员成功']);
    }
}
