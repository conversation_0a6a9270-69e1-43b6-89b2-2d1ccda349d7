<?php

namespace app\controller\api\hotel;

use app\controller\api\Controller;
use app\lib\Date;
use app\lib\Hashids;
use app\model\UserCoupon;
use app\controller\api\hotel\WithHotel;
use think\exception\ValidateException;
use think\annotation\route\Get;
use think\annotation\route\Post;

class CouponController extends Controller
{
    use WithHotel;

    #[Get('hotel/coupon')]
    public function index()
    {
        $coupons = $this->hotel->coupons()
            ->with('info')
            ->order('use_time', 'desc')
            ->paginate();

        return json($coupons);
    }

    #[Get('hotel/coupon/amount')]
    public function amount()
    {
        $amount = $this->hotel->coupons()
            ->with('info')
            ->whereBetween('use_time', [Date::now()->startOfMonth(), Date::now()->endOfMonth()])
            ->select()
            ->reduce(function ($pre, $item) {
                return $pre + $item->info->amount;
            }, 0);

        return json(['amount' => $amount]);
    }

    #[Get('hotel/coupon/{code}')]
    public function read($code)
    {
        $id     = Hashids::decode($code);
        $coupon = UserCoupon::with('info')->whereNull('use_time')->find($id);
        if (!$coupon) {
            throw new ValidateException('优惠券不存在或已使用');
        }
        if (!$coupon->isAvailable()) {
            throw new ValidateException('优惠券已过期');
        }

        return json($coupon);
    }

    #[Post('hotel/coupon/{id}/redeem')]
    public function redeem($id)
    {
        $coupon = UserCoupon::with('info')->whereNull('use_time')->find($id);

        if (!$coupon) {
            throw new ValidateException('优惠券不存在或已使用');
        }
        if (!$coupon->isAvailable()) {
            throw new ValidateException('优惠券已过期');
        }

        $coupon->save([
            'redeemable_type' => 'app\\model\\Hotel',
            'redeemable_id'   => $this->hotel->id,
            'use_time'        => Date::now(),
        ]);
    }
}
