<?php

namespace app\controller\api;

use app\BaseController;
use app\model\OperationLog;
use think\App;
use yunwuxin\Auth;

abstract class Controller extends BaseController
{
    /** @var \app\model\User|null */
    protected $user;

    public function __construct(App $app, Auth $auth)
    {
        parent::__construct($app);
        $this->user = $auth->user();
    }

    protected function validate($validate, array $message = [], $batch = false)
    {
        return parent::validate($validate, $message, $batch);
    }

    /**
     * 记录商家操作日志
     *
     * @param string $action 操作类型
     * @param string $module 操作模块
     * @param string $content 操作内容
     * @param int|null $targetId 操作对象ID
     * @param string|null $targetType 操作对象类型
     */
    protected function log($action, $module, $content, $targetId = null, $targetType = null)
    {
        if (!$this->user) {
            return;
        }

        OperationLog::record(
            $this->user->id,
            OperationLog::USER_TYPE_MERCHANT,
            $action,
            $module,
            $content,
            $targetId,
            $targetType
        );
    }
}
