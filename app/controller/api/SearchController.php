<?php

namespace app\controller\api;

use app\model\Eatery;
use app\model\Hotel;
use app\model\Spot;
use think\annotation\route\Get;

class SearchController extends Controller
{
    #[Get('search/eatery')]
    public function eatery($keyword)
    {
        // 根据名称搜索餐厅
        $eateries = Eatery::where('status', 1)
            ->whereLike('name', '%' . $keyword . '%')
            ->order('rec', 'desc')
            ->order('ord', 'desc')
            ->orderRaw('(stars * 100 + likes * 10 + views) DESC')
            ->order('id', 'desc')
            ->paginate();

        return json($eateries);
    }

    #[Get('search/spot')]
    public function spot($keyword)
    {
        // 根据名称搜索景点
        $spots = Spot::where('status', Spot::STATUS_NORMAL)
            ->whereLike('name', '%' . $keyword . '%')
            ->order('rec', 'desc')
            ->order('ord', 'desc')
            ->orderRaw('(stars * 100 + likes * 10 + views) DESC')
            ->order('id', 'desc')
            ->paginate();

        return json($spots);
    }

    #[Get('search/hotel')]
    public function hotel($keyword)
    {
        // 根据名称搜索酒店
        $hotels = Hotel::where('status', Hotel::STATUS_NORMAL)
            ->whereLike('name', '%' . $keyword . '%')
            ->order('rec', 'desc')
            ->order('ord', 'desc')
            ->order('id', 'desc')
            ->paginate();

        return json($hotels);
    }
}
