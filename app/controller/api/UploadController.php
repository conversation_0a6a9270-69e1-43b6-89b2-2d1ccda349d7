<?php

namespace app\controller\api;

use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\Filesystem;
use yunwuxin\auth\middleware\Authentication;

class UploadController extends Controller
{
    #[Post('upload')]
    #[Middleware(Authentication::class)]
    public function index(Filesystem $filesystem)
    {
        $data = $this->validate([
            'dir'  => 'require|in:avatar,eatery,spot,hotel',
            'file' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');

        $path = $disk->putFile($data['dir'], $data['file']);

        $result = [
            'value' => $path,
        ];

        return json($result);
    }
}
