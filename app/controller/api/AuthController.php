<?php

namespace app\controller\api;

use app\model\User;
use EasyWeChat\MiniApp\Application;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\helper\Arr;
use yunwuxin\auth\middleware\Authentication;

class AuthController extends Controller
{
    #[Get('auth/current')]
    #[Middleware(Authentication::class)]
    public function current()
    {
        return json($this->user->append(['token']));
    }

    #[Post('auth/register')]
    public function register(Application $app)
    {
        $data = $this->validate([
            'mobile' => 'require',
            'code'   => 'require',
        ]);

        $response = $app->getUtils()->codeToSession($data['code']);
        $openid   = Arr::get($response, 'openid');

        $user = User::where('openid', $openid)->find();

        if (!$user) {
            $response = $app->getClient()->postJson('/wxa/business/getuserphonenumber', [
                'code' => $data['mobile'],
            ]);
            $mobile   = Arr::get($response, 'phone_info.phoneNumber');

            $user = User::create([
                'openid'   => $openid,
                'mobile'   => $mobile,
                'nickname' => '微信用户',
            ]);
        }

        return json($user->append(['token']));
    }
}
