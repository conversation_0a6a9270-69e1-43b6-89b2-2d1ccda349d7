<?php
declare (strict_types = 1);

namespace app;

use EasyWeChat\MiniApp\Application;
use Symfony\Component\VarDumper\VarDumper;
use think\App;
use think\Service;

/**
 * 应用服务类
 */
class AppService extends Service
{
    public function register()
    {
        $this->app->resolving(function ($instance, App $container) {
            if ($instance instanceof BaseController) {
                $container->invoke([$instance, 'initialize'], [], true);
            }
        });

        $this->app->bind(Application::class, function () {
            return new Application([
                'app_id' => 'wx65f633c23e85a38b',
                'secret' => 'c933f98223dc2a92c2ba0d5b724aadb3',
            ]);
        });
    }

    public function boot()
    {
        // 服务启动
    }
}
