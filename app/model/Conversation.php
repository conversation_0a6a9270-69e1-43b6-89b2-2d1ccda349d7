<?php

namespace app\model;

use app\lib\Date;
use think\db\Query;
use think\Model;

/**
 * Class app\model\Conversation
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $user_id
 * @property-read \app\model\Message[] $messages
 * @property-read \app\model\User $user
 * @method static \think\db\Query recent()
 */
class Conversation extends Model
{
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function scopeRecent(Query $query)
    {
        //获取最近的一次且在12小时内的会话
        return $query->order('update_time', 'desc')
            ->where('update_time', '>', Date::now()->subHours(12));
    }
}
