<?php

namespace app\model;

use app\lib\Date;
use app\lib\Hashids;
use think\model\Pivot;

/**
 * Class app\model\CouponUser
 *
 * @property \app\lib\Date $create_time 领取时间
 * @property int $coupon_id 优惠券ID
 * @property int $eatery_id 店铺ID (已废弃，使用多态关联)
 * @property string $redeemable_type 核销商家类型
 * @property int $redeemable_id 核销商家ID
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $expire_time 过期时间
 * @property string $use_time 使用时间
 * @property-read \app\model\Coupon $info
 * @property-read \app\model\User $user
 * @property-read mixed $code
 * @property-read \think\Model $redeemable 核销商家（多态关联）
 */
class UserCoupon extends Pivot
{

    protected $type = [
        'use_time'    => Date::class,
        'expire_time' => Date::class,
    ];

    protected $append = ['code'];

    protected function getCodeAttr()
    {
        return Hashids::encode($this->getAttr('id'));
    }

    public function isAvailable()
    {
        return $this->info->isAvailable();
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function info()
    {
        return $this->belongsTo(Coupon::class, 'coupon_id');
    }

    /**
     * 多态关联：核销商家
     */
    public function redeemable()
    {
        return $this->morphTo();
    }

    /**
     * 获取核销商家信息（兼容旧的eatery_id字段）
     */
    public function getRedeemableInfoAttr()
    {
        // 优先使用多态关联
        if ($this->redeemable_type && $this->redeemable_id) {
            return $this->redeemable;
        }

        // 兼容旧的eatery_id字段
        if ($this->eatery_id) {
            return Eatery::find($this->eatery_id);
        }

        return null;
    }
}
