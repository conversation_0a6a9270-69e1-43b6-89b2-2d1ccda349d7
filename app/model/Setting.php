<?php

namespace app\model;

use think\helper\Arr;
use think\Model;
use think\swoole\coroutine\Context;

/**
 * Class app\model\Setting
 *
 * @property string $name
 * @property string $value
 */
class Setting extends Model
{
    protected const DEFAULT = [
        'coupon' => [
            'banner' => '',
        ],
    ];

    protected $autoWriteTimestamp = false;

    public static function read($name, $default = null)
    {
        $name    = explode('.', $name);
        $key     = array_shift($name);
        $setting = self::get($key);
        if (!empty($name)) {
            return Arr::get($setting, implode('.', $name), $default);
        } else {
            return $setting ?? $default;
        }
    }

    public static function write($name, $value)
    {
        $name = explode('.', $name);
        $key  = array_shift($name);
        if (!empty($name)) {
            $setting = self::get($key);
            Arr::set($setting, implode('.', $name), $value);
        } else {
            $setting = $value;
        }

        self::create([
            'name'  => $key,
            'value' => json_encode($setting),
        ], replace: true);
    }

    public static function get($name)
    {
        return Context::rememberData("setting.{$name}", function () use ($name) {
            $setting = self::where('name', $name)->value('value');
            if (!empty($setting)) {
                $setting = json_decode($setting, true);
            } else {
                $setting = [];
            }

            return Arr::mergeDeep(self::DEFAULT[$name] ?? [], $setting);
        });
    }
}
