<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\Hotel
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $ord 排序值
 * @property int $price
 * @property int $rec 推荐值
 * @property int $status
 * @property mixed $images
 * @property mixed $location
 * @property mixed $rating
 * @property string $address
 * @property string $name
 * @property string $type
 * @property-read \app\model\User[] $members 酒店成员
 * @property-read \app\model\User $owner 酒店所有者
 * @property-read \app\model\UserCoupon[] $coupons 优惠券核销记录
 */
class Hotel extends Model
{
    // 酒店状态常量
    const STATUS_PENDING  = 0; // 待审核
    const STATUS_NORMAL   = 1; // 正常
    const STATUS_REJECTED = 2; // 已拒绝
    const STATUS_DISABLED = 3; // 禁用

    protected $json      = ['images', 'location', 'rating'];
    protected $jsonAssoc = true;

    /**
     * 获取酒店成员关联
     */
    public function members()
    {
        return $this->belongsToMany(User::class, HotelMember::class, 'user_id', 'hotel_id');
    }

    /**
     * 获取酒店所有者
     */
    protected function getOwnerAttr()
    {
        return $this->members()->wherePivot('access_level', HotelMember::OWNER)->find();
    }

    /**
     * 获取优惠券核销记录
     */
    public function coupons()
    {
        return $this->morphMany(UserCoupon::class, 'redeemable');
    }
}
