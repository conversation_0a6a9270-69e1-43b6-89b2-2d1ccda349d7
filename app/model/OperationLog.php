<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\OperationLog
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property string $action
 * @property string $content
 * @property int $id
 * @property string $ip
 * @property string $module
 * @property int $target_id
 * @property string $target_type
 * @property int $user_id
 * @property string $user_type
 */
class OperationLog extends Model
{
    /**
     * 关联管理员
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class, 'user_id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // 用户类型
    const USER_TYPE_ADMIN = 'admin';
    const USER_TYPE_MERCHANT = 'merchant';

    // 操作类型
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_APPROVE = 'approve';
    const ACTION_REJECT = 'reject';
    const ACTION_VERIFY = 'verify';
    const ACTION_EXPORT = 'export';

    // 模块
    const MODULE_ADMIN = 'admin';
    const MODULE_EATERY = 'eatery';
    const MODULE_HOTEL = 'hotel';
    const MODULE_SPOT = 'spot';
    const MODULE_COUPON = 'coupon';
    const MODULE_USER = 'user';
    const MODULE_RESERVATION = 'reservation';
    const MODULE_FOOD = 'food';
    const MODULE_SYSTEM = 'system';

    /**
     * 记录操作日志
     *
     * @param int $userId 用户ID
     * @param string $userType 用户类型
     * @param string $action 操作类型
     * @param string $module 操作模块
     * @param string $content 操作内容
     * @param int|null $targetId 操作对象ID
     * @param string|null $targetType 操作对象类型
     * @return OperationLog
     */
    public static function record($userId, $userType, $action, $module, $content, $targetId = null, $targetType = null)
    {
        $request = request();

        return self::create([
            'user_id' => $userId,
            'user_type' => $userType,
            'action' => $action,
            'module' => $module,
            'content' => $content,
            'target_id' => $targetId,
            'target_type' => $targetType,
            'ip' => $request->ip(),
            // 不再保存user_agent信息
        ]);
    }
}
