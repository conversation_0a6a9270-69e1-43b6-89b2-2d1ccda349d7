<?php

namespace app\model;

use Firebase\JWT\JWT;
use think\Model;

/**
 * Class app\model\User
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property string $avatar
 * @property string $mobile
 * @property string $nickname
 * @property string $openid
 * @property-read \app\model\Coupon[] $coupons
 * @property-read \app\model\Eatery[] $eateries
 * @property-read \app\model\Eatery[] $star_eateries
 * @property-read \app\model\Spot[] $star_spots
 * @property-read \app\model\Spot[] $spots
 * @property-read \app\model\Hotel[] $hotels
 * @property-read \app\model\Reservation[] $reservations
 * @property-read mixed $token
 */
class User extends Model
{
    public function eateries()
    {
        return $this->belongsToMany(Eatery::class, EateryMember::class, 'eatery_id', 'user_id');
    }

    public function starEateries()
    {
        return $this->belongsToMany(Eatery::class, EateryStar::class, 'eatery_id', 'user_id');
    }

    public function coupons()
    {
        return $this->belongsToMany(Coupon::class, UserCoupon::class, 'coupon_id', 'user_id');
    }

    public function starSpots()
    {
        return $this->belongsToMany(Spot::class, SpotStar::class, 'spot_id', 'user_id');
    }

    public function spots()
    {
        return $this->belongsToMany(Spot::class, SpotMember::class, 'spot_id', 'user_id');
    }

    public function hotels()
    {
        return $this->belongsToMany(Hotel::class, HotelMember::class, 'hotel_id', 'user_id');
    }

    /**
     * 获取用户的预订记录
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class, 'user_id', 'id');
    }

    protected function getTokenAttr()
    {
        return JWT::encode([
            'exp' => time() + 60 * 60 * 24 * 3,
            'iat' => time(),
            'id'  => $this->id,
        ], config('app.token'), 'HS256');
    }
}
