<?php

namespace app\model;

use app\lib\Date;
use think\db\Query;
use think\Model;

/**
 * Class app\model\Coupon
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property float $amount 优惠券金额
 * @property int $claimed
 * @property int $id
 * @property int $nums
 * @property int $status
 * @property Date $end_time 结束时间
 * @property string $name 优惠券名称
 * @property string $rules 使用规则
 * @property Date $start_time 开始时间
 * @property-read \app\model\User[] $users
 * @method static \think\db\Query available()
 */
class Coupon extends Model
{

    protected $type = [
        'start_time' => Date::class,
        'end_time'   => Date::class,
    ];

    public function isAvailable()
    {
        $now = Date::now()->startOfDay();
        return $this->status == 1 &&
            $this->start_time <= $now && $this->end_time >= $now &&
            ($this->claimed < $this->nums || $this->nums == 0);
    }

    public function scopeAvailable(Query $query)
    {
        $now = Date::now()->startOfDay();
        $query->where('status', 1)
            ->where(function (Query $query) {
                $query->whereRaw('claimed<nums')->whereOr('nums', 0);
            })
            ->where('start_time', '<=', $now)
            ->where('end_time', '>=', $now);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, UserCoupon::class, 'user_id', 'coupon_id');
    }
}
