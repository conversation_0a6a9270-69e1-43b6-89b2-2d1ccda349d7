<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\Reservation
 *
 * @property \app\lib\Date $create_time 创建时间（预订日期）
 * @property \app\lib\Date $update_time
 * @property int $eatery_id 餐厅ID
 * @property int $id
 * @property int $status 预订状态：0=已取消，1=已预订
 * @property int $user_id 用户ID
 * @property string $reservation_time 预订时间段，格式：YYYY-MM-DD HH:MM-HH:MM
 * @property int $people_count 预订人数
 * @property-read \app\model\Eatery $eatery
 * @property-read \app\model\User $user
 */
class Reservation extends Model
{
    // 预订状态常量
    const STATUS_PENDING   = 0;    // 确认中
    const STATUS_CONFIRMED = 1;  // 已确认
    const STATUS_CANCELLED = 2;  // 已取消（商家取消）

    protected $append = ['status_text'];

    /**
     * 获取业务日期的开始时间（凌晨3点）
     * 如果当前时间是凌晨0-3点，则返回前一天的凌晨3点
     *
     * @return \app\lib\Date
     */
    public static function getBusinessDayStart()
    {
        $now = Date::now();
        $hour = (int)$now->format('H');

        // 如果当前时间是凌晨0-3点，则返回前一天的凌晨3点
        if ($hour < 3) {
            return $now->subDay()->startOfDay()->addHours(3);
        }

        // 否则返回当天凌晨3点
        return $now->startOfDay()->addHours(3);
    }

    /**
     * 获取关联的餐厅
     */
    public function eatery()
    {
        return $this->belongsTo(Eatery::class, 'eatery_id');
    }

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取用户当天对指定餐厅的预订状态
     *
     * @param int $eateryId 餐厅ID
     * @param int $userId 用户ID
     * @return int 预订状态：-1=不支持预订，0=未预订，1=确认中，2=已确认
     */
    public static function isReserved($eateryId, $userId)
    {
        // 首先检查餐厅是否支持预订
        $eatery = Eatery::find($eateryId);
        if (!$eatery || $eatery->status != Eatery::STATUS_APPROVED || $eatery->can_reserve != 1) {
            return -1; // 不支持预订
        }

        // 获取业务日期的开始时间（考虑凌晨0-3点的情况）
        $today = self::getBusinessDayStart();
        // 明天凌晨3点结束
        $tomorrow = $today->copy()->addDay();

        // 查找用户当天的预订记录
        $reservation = self::where('eatery_id', $eateryId)
            ->where('user_id', $userId)
            ->where('create_time', '>=', $today)
            ->where('create_time', '<', $tomorrow)
            ->whereIn('status', [self::STATUS_PENDING, self::STATUS_CONFIRMED])
            ->find();

        if (!$reservation) {
            return 0; // 未预订
        }

        // 返回具体的预订状态
        return $reservation->status + 1; // STATUS_PENDING(0) -> 1, STATUS_CONFIRMED(1) -> 2
    }

    /**
     * 判断预订是否有效（是否是当天的预订）
     *
     * @return bool 是否有效
     */
    public function isValid()
    {
        // 获取业务日期的开始时间（考虑凌晨0-3点的情况）
        $today = self::getBusinessDayStart();
        // 明天凌晨3点结束
        $tomorrow = $today->copy()->addDay();

        return ($this->status === self::STATUS_PENDING || $this->status === self::STATUS_CONFIRMED) &&
            $this->create_time >= $today &&
            $this->create_time < $tomorrow;
    }

    /**
     * 获取预订的状态文本
     *
     * @return string 状态文本
     */
    protected function getStatusTextAttr()
    {
        // 获取业务日期的开始时间（考虑凌晨0-3点的情况）
        $today = self::getBusinessDayStart();

        if ($this->status === self::STATUS_CANCELLED) {
            return '已取消';
        }

        if ($this->status === self::STATUS_CONFIRMED) {
            if ($this->create_time < $today) {
                return '已过期';
            }
            return '已确认';
        }

        if ($this->create_time < $today) {
            return '已过期';
        }

        return '确认中';
    }
}
