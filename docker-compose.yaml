services:
  app:
    image: registry.zaocha.net.cn/zaocha:latest
    pull_policy: always
    restart: always
    command:
      - app:run
    environment:
      PHP_DB_HOST: mysql
      PHP_REDIS_HOST: redis
      PHP_CACHE_TYPE: redis
    volumes:
      - ./data/storage:/opt/htdocs/storage
    depends_on:
      - mysql
      - redis

  redis:
    image: redis:latest
    restart: always
    volumes:
      - ./data/redis:/data

  mysql:
    image: mysql:8.4.5
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=zaocha
    volumes:
      - ./data/mysql:/var/lib/mysql
