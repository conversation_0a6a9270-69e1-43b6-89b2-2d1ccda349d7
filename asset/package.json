{"name": "sszc", "version": "1.0.0", "description": "", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev"}, "devDependencies": {"@topthink/webpack-config-plugin": "^1.0.6", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "babel-plugin-styled-components": "^1.13.1", "bootstrap": "^5.3.3", "typescript": "^4.6.4", "webpack": "^5.85.1", "webpack-cli": "^5.1.3", "webpack-dev-server": "^4.15.0"}, "dependencies": {"@topthink/common": "^1.5.18", "bootstrap-icons": "^1.11.3", "react": "^18.0.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.0.0"}}