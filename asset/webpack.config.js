const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");

module.exports = (env) => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : "./src/main.tsx",
        cache    : {
            type: "filesystem"
        },
        output   : {
            filename     : "app.[contenthash:6].js",
            chunkFilename: "[id].[contenthash:6].js",
            path         : path.resolve(__dirname, "dist/asset"),
            clean        : true,
            publicPath   : isServer ? "/" : "/asset/"
        },
        externals: isDevelopment ? {
            "react"           : "React",
            "react-dom"       : "ReactDOM",
            "react-dom/client": "ReactDOM"
        } : {},
        plugins  : [
            new WebpackConfigPlugin({
                serve            : isServer,
                html             : {
                    filename          : isServer ? "index.html" : "../index.html",
                    template          : "public/index.ejs",
                    inject            : false,
                    scriptLoading     : "blocking",
                    favicon           : "public/favicon.ico",
                    templateParameters: {
                        isDevelopment
                    }
                },
                react            : true,
                externalizeLodash: false
            })
        ],
        devServer: {
            hot               : true,
            historyApiFallback: true,
            proxy             : {
                "/admin"  : {
                    target      : "http://dev.zaocha.net.cn",
                    changeOrigin: true
                },
                "/uploads": {
                    target      : "http://dev.zaocha.net.cn",
                    changeOrigin: true
                }
            }
        }
    };
};
