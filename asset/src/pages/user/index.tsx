import { Content, Table } from '@topthink/common';

export const Component = () => {
    return <Content>
        <Table
            source={'user'}
            columns={[
                {
                    title: '昵称',
                    dataIndex: 'nickname',
                },
                {
                    title: '手机号',
                    dataIndex: 'mobile',
                    width: 120
                },
                {
                    title: '已核销/已领取',
                    width: 120,
                    align: 'center',
                    render({ record }) {
                        return `${record.used_coupons}/${record.claimed_coupons}`;
                    }
                },
                {
                    title: '注册时间',
                    dataIndex: 'create_time',
                    width: 180
                }
            ]}
        />
    </Content>;
};
