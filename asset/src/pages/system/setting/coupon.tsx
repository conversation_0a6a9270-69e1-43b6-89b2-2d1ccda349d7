import getImageUrl from '@/utils';
import { Card, Form, Loader, useRequest } from '@topthink/common';

export const Component = () => {
    const { result } = useRequest('/setting/coupon');

    if (!result) {
        return <Loader />;
    }

    return <Card>
        <Form
            method={'put'}
            action={'/setting/coupon'}
            formData={result}
            schema={{
                type: 'object',
                properties: {
                    banner: {
                        type: 'string',
                        title: '入口图片',
                    },
                }
            }}
            uiSchema={{
                banner: {
                    'ui:widget': 'upload',
                    'ui:endpoint': '/upload/setting',
                    'ui:renderIcon': (url: string) => {
                        return <img height={100} src={getImageUrl(url)} />;
                    }
                }
            }}
        />
    </Card>;
};
