import { Content, ModalForm, RequestButton, Space, Table, useUser } from '@topthink/common';
import AdminForm from './admin-form';

export const Component = () => {
    // 获取当前用户信息
    const [user] = useUser();

    // 检查用户是否有权限（管理员角色）
    const isAdmin = user?.access_level === 60;

    return <Content>
        <Table
            toolBarRender={({ reload }) => {
                // 只有管理员可以新建管理员
                return isAdmin ? (
                    <AdminForm
                        text={'新建管理员'}
                        action={'admin'}
                        method={'post'}
                        onSuccess={reload}
                        modalProps={{ header: '新建管理员' }}
                    />
                ) : null;
            }}
            source={'admin'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 80
                },
                {
                    title: '用户名',
                    dataIndex: 'username',
                },
                {
                    title: '角色',
                    dataIndex: 'access_level',
                    width: 100,
                    render: ({ value }) => {
                        return value === 60 ? '管理' : '运营';
                    }
                },
                {
                    title: '创建时间',
                    dataIndex: 'create_time',
                    width: 180
                },
                {
                    title: '操作',
                    align: 'right',
                    key: 'action',
                    render: ({ record, action }) => {
                        // 超级管理员（ID为1）不允许删除
                        const isSuper = record.id === 1;

                        return <Space>
                            {/* 只有管理员可以编辑管理员 */}
                            {isAdmin && (
                                <AdminForm
                                    text={'编辑'}
                                    modalProps={{ header: '编辑管理员' }}
                                    action={`admin/${record.id}`}
                                    method={'put'}
                                    onSuccess={action.reload}
                                    formData={record}
                                />
                            )}

                            {/* 修改密码功能对所有管理员开放，包括运营员 */}
                            <ModalForm
                                text={'修改密码'}
                                modalProps={{ header: '修改密码' }}
                                action={`admin/${record.id}/password`}
                                method={'post'}
                                onSuccess={action.reload}
                                formContext={{ layout: 'horizontal' }}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        password: {
                                            type: 'string',
                                            title: '密码',
                                            minLength: 6
                                        }
                                    }
                                }}
                                uiSchema={{
                                    password: {
                                        'ui:widget': 'password'
                                    }
                                }}
                            />

                            {/* 只有管理员可以删除管理员，且不能删除超级管理员 */}
                            {!isSuper && isAdmin && (
                                <RequestButton
                                    url={`admin/${record.id}`}
                                    method={'delete'}
                                    onSuccess={action.reload}
                                    confirm={`确定要删除管理员 "${record.username}" 吗？此操作不可恢复！`}
                                >删除</RequestButton>
                            )}
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
