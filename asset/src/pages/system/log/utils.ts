// 格式化操作类型
export const formatAction = (action: string): string => {
    const actionMap: Record<string, string> = {
        'create': '创建',
        'update': '更新',
        'delete': '删除',
        'login': '登录',
        'logout': '登出',
        'approve': '审核通过',
        'reject': '拒绝',
        'verify': '核销',
        'export': '导出'
    };
    return actionMap[action] || action;
};

// 格式化模块
export const formatModule = (module: string): string => {
    const moduleMap: Record<string, string> = {
        'admin': '管理员',
        'eatery': '商家',
        'hotel': '酒店',
        'spot': '景点',
        'coupon': '优惠券',
        'user': '用户',
        'reservation': '预订',
        'food': '菜品',
        'system': '系统设置'
    };
    return moduleMap[module] || module;
};
