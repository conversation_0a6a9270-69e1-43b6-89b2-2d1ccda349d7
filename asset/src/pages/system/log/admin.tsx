import { Table } from '@topthink/common';
import { formatAction, formatModule } from './utils';

export const Component = () => {
    return (
        <Table
            source={'logs/admin'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '用户',
                    dataIndex: ['admin', 'username'],
                    width: 120
                },
                {
                    title: '操作类型',
                    dataIndex: 'action',
                    width: 100,
                    render: ({ value }: { value: string }) => formatAction(value)
                },
                {
                    title: '操作模块',
                    dataIndex: 'module',
                    width: 100,
                    render: ({ value }: { value: string }) => formatModule(value)
                },
                {
                    title: '操作内容',
                    dataIndex: 'content',
                },
                {
                    title: 'IP地址',
                    dataIndex: 'ip',
                    width: 120
                },
                {
                    title: '操作时间',
                    dataIndex: 'create_time',
                    width: 180
                }
            ]}
        />
    );
};
