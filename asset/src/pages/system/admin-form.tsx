import { ModalForm, ModalFormProps, FormSchema } from '@topthink/common';

// 管理员信息表单属性
interface AdminFormProps extends Partial<ModalFormProps> {
    text: string;
}

// 管理员信息表单
export default function AdminForm({ formData, ...props }: AdminFormProps) {
    // 检查是否是超级管理员（ID为1）
    const isSuperAdmin = formData?.id === 1;

    // 管理员信息 schema
    const adminSchema: FormSchema = {
        type: 'object',
        properties: {
            username: {
                type: 'string',
                title: '用户名',
            },
            // 如果是超级管理员，不显示角色选择
            ...(!isSuperAdmin ? {
                access_level: {
                    type: 'integer',
                    title: '角色',
                    enum: [60, 30],
                    enumNames: ['管理', '运营']
                }
            } : {})
        }
    };

    // 预设权限级别为管理员(60)
    const defaultData = {
        access_level: 60
    };

    // 新建管理员时需要密码字段
    const schema: FormSchema = formData ? adminSchema : {
        ...adminSchema,
        properties: {
            ...adminSchema.properties,
            password: {
                type: 'string',
                title: '密码',
                minLength: 6
            }
        }
    };

    // 准备UI Schema
    const uiSchema: any = {
        password: {
            'ui:widget': 'password'
        }
    };

    // 如果不是超级管理员，添加角色选择的UI设置
    if (!isSuperAdmin) {
        uiSchema.access_level = {
            'ui:widget': 'radio'
        };
    }

    // 如果是超级管理员，确保角色为管理员
    const finalFormData = formData ?
        (isSuperAdmin ? { ...formData, access_level: 60 } : formData) :
        defaultData;

    return <ModalForm
        {...props}
        formContext={{ layout: 'horizontal' }}
        schema={schema}
        uiSchema={uiSchema}
        formData={finalFormData}
    />;
}
