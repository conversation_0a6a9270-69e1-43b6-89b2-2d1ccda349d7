import { ModalForm, ModalFormProps } from '@topthink/common';

interface Props extends Partial<ModalFormProps> {
    text: string;
}

export default function Form({ formData, ...props }: Props) {
    if (formData) {
        formData.date = [formData.start_time, formData.end_time];
        delete formData.start_time;
        delete formData.end_time;
    }
    return <ModalForm
        {...props}
        transformData={({ date = [null, null], ...data }) => {
            return {
                ...data,
                start_time: date[0],
                end_time: date[1]
            };
        }}
        formContext={{ layout: 'horizontal' }}
        schema={{
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '名称',
                },
                amount: {
                    type: 'number',
                    title: '金额',
                },
                nums: {
                    type: 'number',
                    title: '数量',
                },
                date: {
                    type: 'string',
                    title: '活动时间',
                },
                rules: {
                    type: 'string',
                    title: '使用规则',
                }
            }
        }}
        uiSchema={{
            date: {
                'ui:widget': 'date-range'
            },
            rules: {
                'ui:widget': 'textarea',
                'ui:options': {
                    rows: 4,
                    placeholder: '请输入优惠券的使用规则'
                }
            }
        }}
        formData={formData}
    />;
}
