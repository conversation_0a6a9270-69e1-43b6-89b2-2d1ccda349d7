import { Content, ModalButton, Space, dayjs, Table, RequestButton, useUser } from '@topthink/common';
import Form from './form';

export const Component = () => {
    // 获取当前用户信息
    const [user] = useUser();

    // 检查用户是否有权限（管理员角色）
    const isAdmin = user?.access_level === 60;

    return <Content>
        <Table
            toolBarRender={({ reload }) => {
                // 只有管理员可以新建消费券
                return isAdmin ? (
                    <Form
                        text={'新建消费券'}
                        action={'coupon'}
                        method={'post'}
                        onSuccess={reload}
                    />
                ) : null;
            }}
            source={'coupon'}
            columns={[
                {
                    title: '名称',
                    dataIndex: 'name',
                },
                {
                    title: '金额',
                    dataIndex: 'amount',
                    valueType: 'currency',
                },
                {
                    title: '已核销/已领取/总数',
                    dataIndex: 'nums',
                    align: 'center',
                    render({ record }) {
                        // 使用后端提供的 used 字段
                        return `${record.used}/${record.claimed}/${record.nums}`;
                    }
                },
                {
                    title: '活动时间',
                    dataIndex: 'date',
                    render: ({ record }) => {
                        return `${dayjs(record.start_time).format('YYYY-MM-DD')}-${dayjs(record.end_time)
                        .format('YYYY-MM-DD')}`;
                    }
                },
                {
                    title: '操作',
                    align: 'right',
                    key: 'action',
                    render: ({ record, action }) => {
                        return <Space>
                            {/* 只有管理员可以编辑消费券 */}
                            {isAdmin && (
                                <Form
                                    text={'编辑'}
                                    modalProps={{ header: '编辑消费券' }}
                                    action={`coupon/${record.id}`}
                                    method={'put'}
                                    onSuccess={action.reload}
                                    formData={record}
                                />
                            )}

                            <ModalButton text={'核销记录'} modalProps={{ size: 'xl', footer: false }}>
                                <Table
                                    card={false}
                                    toolBarRender={() => {
                                        return <RequestButton
                                            url={`coupon/${record.id}/records/export`}
                                            method={'post'}
                                            variant={'primary'}
                                            onSuccess={({ url }) => {
                                                if (url) {
                                                    window.open(url, '_blank');
                                                }
                                            }}
                                        >导出</RequestButton>;
                                    }}
                                    source={`/coupon/${record.id}/records`}
                                    columns={[
                                        {
                                            title: '用户',
                                            dataIndex: ['user', 'nickname'],
                                        },
                                        {
                                            title: '手机号',
                                            dataIndex: ['user', 'mobile'],
                                            width: 120,
                                        },
                                        {
                                            title: '券码',
                                            dataIndex: 'code',
                                            width: 120,
                                        },
                                        {
                                            title: '金额',
                                            dataIndex: ['info', 'amount'],
                                            valueType: 'currency',
                                            width: 80,
                                        },
                                        {
                                            title: '商家',
                                            dataIndex: 'merchant_name',
                                            width: 150,
                                            render({ record }) {
                                                // 优先显示多态关联的商家信息
                                                if (record.merchant) {
                                                    const type = record.merchant_type || 'Eatery';
                                                    const typeMap = {
                                                        'Eatery': '餐厅',
                                                        'Spot': '景点',
                                                        'Hotel': '酒店'
                                                    };
                                                    return `${record.merchant.name} (${typeMap[type] || type})`;
                                                }
                                                // 兼容旧数据
                                                if (record.eatery) {
                                                    return `${record.eatery.name} (餐厅)`;
                                                }
                                                return '-';
                                            }
                                        },
                                        {
                                            title: '核销时间',
                                            dataIndex: 'use_time',
                                            width: 180,
                                            render({ value }) {
                                                return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
                                            }
                                        }
                                    ]}
                                />
                            </ModalButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
