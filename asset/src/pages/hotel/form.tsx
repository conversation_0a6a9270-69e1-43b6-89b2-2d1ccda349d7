import {FormWidgetProps, ModalButton, ModalForm, ModalFormProps, queryString, Space} from '@topthink/common';
import {useEffect, useMemo, useState} from 'react';
import getImageUrl from '../../utils';

interface Props extends Partial<ModalFormProps> {
    text: string;
}

export default function HotelForm({formData, ...props}: Props) {
    return <ModalForm
        {...props}
        formContext={{layout: 'horizontal'}}
        formData={formData}
        schema={{
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '酒店名称',
                },
                type: {
                    type: 'string',
                    title: '酒店类型',
                },
                images: {
                    type: 'array',
                    title: '酒店图片',
                    items: {
                        type: 'string',
                    }
                },
                location: {
                    type: 'string',
                    title: '位置坐标',
                },
                address: {
                    type: 'string',
                    title: '详细地址',
                },
                path: {
                    type: 'string',
                    title: '携程地址',
                },
                price: {
                    type: 'number',
                    title: '价格',
                },
                rating: {
                    type: 'object',
                    title: '评价',
                    properties: {
                        score: {
                            type: 'number',
                            title: '评分',
                            minimum: 0,
                            maximum: 5
                        },
                        comment: {
                            type: 'string',
                            title: '评语'
                        }
                    }
                }
            }
        }}
        uiSchema={{
            images: {
                'ui:widget': 'upload',
                'ui:endpoint': '/upload/hotel',
                'ui:renderIcon': (url: string) => {
                    return <img height={48} src={getImageUrl(url)}/>;
                }
            },
            address: {
                'ui:widget': 'textarea'
            },
            location: {
                'ui:widget': function ({value, onChange}: FormWidgetProps) {
                    const [location, setLocation] = useState<{
                        name: string,
                        address: string,
                        latitude: number,
                        longitude: number,
                    }>();

                    useEffect(() => {
                        const listener = function(event: MessageEvent) {
                            // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
                            const loc = event.data;
                            if (loc && loc.module == 'locationPicker') {//防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
                                setLocation({
                                    name: loc.poiname,
                                    address: loc.poiaddress,
                                    latitude: loc.latlng.lat,
                                    longitude: loc.latlng.lng,
                                });
                            }
                        };
                        window.addEventListener('message', listener, false);
                        return () => {
                            window.removeEventListener('message', listener, false);
                        };
                    }, []);

                    const url = useMemo(() => {
                        return queryString.stringifyUrl({
                            url: 'https://apis.map.qq.com/tools/locpicker',
                            query: {
                                search: 1,
                                type: 1,
                                key: 'W6PBZ-2OWWC-V6N27-A7I3B-2PF3Z-RJB55',
                                referer: 'admin',
                                coord: value ? `${value.longitude},${value.latitude}` : undefined,
                            }
                        });
                    }, [value]);

                    return <Space>
                        <ModalButton
                            text={'选择位置'}
                            variant={'light'}
                            modalProps={{
                                size: 'lg',
                                onCancel: () => {
                                    setLocation(undefined);
                                }
                            }}
                            onOk={() => {
                                if (!location) {
                                    return false;
                                }
                                onChange(location);
                            }}
                        >
                            <div style={{margin: '-1rem', height: 'calc(100vh - 15rem)'}}>
                                <iframe
                                    width='100%'
                                    height='100%'
                                    frameBorder={0}
                                    src={url}
                                />
                            </div>
                        </ModalButton>
                        {value && <span className={'text-muted'}>{value.name}</span>}
                    </Space>;
                }
            },
            rating: {
                'ui:fieldset': false,
                score: {
                    'ui:col': 3,
                },
                comment: {
                    'ui:col': 9
                }
            }
        }}
    />;
}
