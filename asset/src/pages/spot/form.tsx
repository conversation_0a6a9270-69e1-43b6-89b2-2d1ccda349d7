import { FormWidgetProps, ModalButton, ModalForm, ModalFormProps, Space, queryString } from '@topthink/common';
import { useEffect, useMemo, useState } from 'react';
import getImageUrl from '../../utils';

interface Props extends Partial<ModalFormProps> {
    text: string;
}

export default function SpotForm({ formData, ...props }: Props) {
    return <ModalForm
        {...props}
        formContext={{ layout: 'horizontal' }}
        schema={{
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '景点名称',
                },
                images: {
                    type: 'array',
                    title: '景点图片',
                    items: {
                        type: 'string',
                    }
                },
                hours: {
                    type: 'object',
                    title: '开放时间',
                    properties: {
                        start: {
                            type: 'string',
                            title: '开始时间'
                        },
                        end: {
                            type: 'string',
                            title: '结束时间'
                        }
                    }
                },
                location: {
                    type: 'string',
                    title: '位置坐标',
                },
                address: {
                    type: 'string',
                    title: '详细地址',
                },
                phone: {
                    type: 'string',
                    title: '联系电话',
                },
            }
        }}
        uiSchema={{
            images: {
                'ui:widget': 'upload',
                'ui:endpoint': '/upload/spot',
                'ui:renderIcon': (url: string) => {
                    return <img height={48} src={getImageUrl(url)} />;
                }
            },
            hours: {
                'ui:fieldset': false,
                start: {
                    'ui:widget': 'time',
                    'ui:col': 3,
                    'ui:label': false,
                    'ui:placeholder': '开始时间',
                    'ui:second': false
                },
                end: {
                    'ui:widget': 'time',
                    'ui:col': 3,
                    'ui:label': false,
                    'ui:placeholder': '结束时间',
                    'ui:second': false
                }
            },
            location: {
                'ui:widget': function({ value, onChange }: FormWidgetProps) {
                    const [location, setLocation] = useState<{
                        name: string,
                        latitude: number,
                        longitude: number,
                    }>();

                    useEffect(() => {
                        const listener = function(event: MessageEvent) {
                            // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
                            const loc = event.data;
                            if (loc && loc.module == 'locationPicker') {//防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
                                setLocation({
                                    name: loc.poiaddress,
                                    latitude: loc.latlng.lat,
                                    longitude: loc.latlng.lng,
                                });
                            }
                        };
                        window.addEventListener('message', listener, false);
                        return () => {
                            window.removeEventListener('message', listener, false);
                        };
                    }, []);

                    const url = useMemo(() => {
                        return queryString.stringifyUrl({
                            url: 'https://apis.map.qq.com/tools/locpicker',
                            query: {
                                search: 1,
                                type: 1,
                                key: 'W6PBZ-2OWWC-V6N27-A7I3B-2PF3Z-RJB55',
                                referer: 'admin',
                                coord: value ? `${value.longitude},${value.latitude}` : undefined,
                            }
                        });
                    }, [value]);

                    return <Space>
                        <ModalButton
                            text={'选择位置'}
                            variant={'light'}
                            modalProps={{
                                size: 'lg',
                                onCancel: () => {
                                    setLocation(undefined);
                                }
                            }}
                            onOk={() => {
                                if (!location) {
                                    return false;
                                }
                                onChange(location);
                            }}
                        >
                            <div style={{ margin: '-1rem', height: 'calc(100vh - 15rem)' }}>
                                <iframe
                                    width='100%'
                                    height='100%'
                                    frameBorder={0}
                                    src={url}
                                />
                            </div>
                        </ModalButton>
                        {value && <span className={'text-muted'}>{value.name}</span>}
                    </Space>;
                }
            },
            address: {
                'ui:widget': 'textarea'
            },
            phone: {
                'ui:widget': 'text'
            }
        }}
        formData={formData}
    />;
}
