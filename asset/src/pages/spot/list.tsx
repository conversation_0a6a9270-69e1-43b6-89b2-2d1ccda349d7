import {Content, ModalButton, ModalForm, RequestButton, Space, Table, dayjs, useUser} from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import getImageUrl from '../../utils';
import SpotForm from './form';

export const Component = () => {
    // 获取当前用户信息
    const [user] = useUser();

    // 检查用户是否有权限
    const isAdmin = user?.access_level === 60; // 管理员角色
    return <Content>
        <Table
            search
            toolBarRender={({ reload }) => {
                return <Space>
                    <SpotForm
                        text={'新建景点'}
                        action={'spot'}
                        method={'post'}
                        onSuccess={reload}
                        modalProps={{ header: '新建景点', size: 'xl' }}
                    />
                    <RequestButton
                        url={'spot/export'}
                        method={'post'}
                        variant={'secondary'}
                        onSuccess={({ url }) => {
                            if (url) {
                                window.open(url, '_blank');
                            }
                        }}
                    >导出</RequestButton>
                </Space>;
            }}
            source={'spot'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '景点名称',
                    dataIndex: 'name',
                    render({ record }) {
                        if (record.images && record.images.length > 0) {
                            return <Space>
                                <img className={'rounded'} src={getImageUrl(record.images[0])} width={30} height={30} />
                                {record.name}
                            </Space>;
                        }
                        return record.name;
                    }
                },
                {
                    title: '区县',
                    dataIndex: ['location', 'district'],
                    width: 80
                },
                {
                    title: '地址',
                    dataIndex: 'address',
                    width: 250
                },
                {
                    title: '店主',
                    dataIndex: 'owner',
                    width: 180,
                    render({ value }) {
                        return value ? `${value.nickname}(${value.mobile})` : '-';
                    }
                },
                {
                    title: '收藏/点赞/浏览',
                    width: 120,
                    align: 'center',
                    render({ record }) {
                        return <span style={{ whiteSpace: 'nowrap' }}>
                            {record.stars} / {record.likes} / {record.views}
                        </span>;
                    }
                },

                {
                    title: '推荐值',
                    width: 80,
                    align: 'center',
                    dataIndex: 'rec',
                    render({ value, record, action }) {
                        // 只有管理员可以修改推荐值
                        if (!isAdmin) {
                            return value || 0;
                        }

                        return <ModalForm
                            text={value || 0}
                            modalProps={{ header: '设置推荐值' }}
                            action={`spot/${record.id}/rec`}
                            method={'put'}
                            onSuccess={action.reload}
                            formData={{ rec: value || 0 }}
                            schema={{
                                type: 'object',
                                properties: {
                                    rec: {
                                        type: 'number',
                                        title: '推荐值',
                                        description: '值越大越靠前',
                                    }
                                }
                            }}
                        />;
                    },
                },
                {
                    title: '排序值',
                    width: 80,
                    align: 'center',
                    dataIndex: 'ord',
                    render({ value, record, action }) {
                        // 只有管理员可以修改排序值
                        if (!isAdmin) {
                            return value || 0;
                        }

                        return <ModalForm
                            text={value}
                            modalProps={{ header: '设置排序值' }}
                            action={`spot/${record.id}/ord`}
                            method={'put'}
                            onSuccess={action.reload}
                            formData={{ ord: value }}
                            schema={{
                                type: 'object',
                                properties: {
                                    ord: {
                                        type: 'number',
                                        title: '排序值',
                                        description: '值越大越靠前',
                                    }
                                }
                            }}
                        />;
                    },
                },
                {
                    title: '状态',
                    width: 60,
                    dataIndex: 'status',
                    render({ value }) {
                        return value === 1 ?
                            <span style={{ color: '#52c41a' }}>正常</span> :
                            <span style={{ color: '#ff4d4f' }}>禁用</span>;
                    },
                },
                {
                    title: '操作',
                    width: 60,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <Dropdown align={'end'}>
                                <Dropdown.Toggle
                                    className={'border-0 no-caret'}
                                    variant={'outline-secondary'}
                                ><i className='bi bi-three-dots-vertical' /></Dropdown.Toggle>
                                <Dropdown.Menu className={'shadow'} renderOnMount popperConfig={{ strategy: 'fixed' }}>
                                    <ModalForm
                                        method={'post'}
                                        action={`spot/${record.id}/owner`}
                                        buttonProps={{ as: Dropdown.Item }}
                                        text={'设置店主'}
                                        schema={{
                                            type: 'object',
                                            properties: {
                                                user_id: {
                                                    type: 'number',
                                                }
                                            }
                                        }}
                                        uiSchema={{
                                            user_id: {
                                                'ui:widget': 'typeahead',
                                                'ui:label': false,
                                                'ui:options': {
                                                    endpoint: 'user/search',
                                                    minLength: 0,
                                                }
                                            }
                                        }}
                                        onSuccess={action.reload}
                                        modalProps={{ header: '设置店主' }}
                                    />
                                    <ModalButton as={Dropdown.Item} text={'员工列表'} modalProps={{
                                        size: 'xl',
                                        footer: false
                                    }}>
                                        <Table
                                            card={false}
                                            toolBarRender={false}
                                            source={`/spot/${record.id}/members`}
                                            columns={[
                                                {
                                                    title: '姓名',
                                                    dataIndex: 'nickname',
                                                },
                                                {
                                                    title: '手机号',
                                                    dataIndex: 'mobile',
                                                    width: 120,
                                                },
                                                {
                                                    title: '加入时间',
                                                    dataIndex: ['pivot', 'create_time'],
                                                    width: 180,
                                                    render({ value }) {
                                                        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
                                                    }
                                                }
                                            ]}
                                        />
                                    </ModalButton>
                                    <ModalButton as={Dropdown.Item} text={'核销记录'} modalProps={{
                                        size: 'xl',
                                        footer: false
                                    }}>
                                        <Table
                                            card={false}
                                            toolBarRender={false}
                                            source={`/spot/${record.id}/coupons`}
                                            columns={[
                                                {
                                                    title: '消费券',
                                                    dataIndex: ['info', 'name'],
                                                },
                                                {
                                                    title: '金额',
                                                    dataIndex: ['info', 'amount'],
                                                    valueType: 'currency',
                                                    width: 80,
                                                },
                                                {
                                                    title: '用户',
                                                    dataIndex: ['user', 'nickname'],
                                                },
                                                {
                                                    title: '手机号',
                                                    dataIndex: ['user', 'mobile'],
                                                    width: 120,
                                                },
                                                {
                                                    title: '核销时间',
                                                    dataIndex: 'use_time',
                                                    width: 180,
                                                    render({ value }) {
                                                        return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
                                                    }
                                                }
                                            ]}
                                        />
                                    </ModalButton>
                                    <SpotForm
                                        text={'编辑景点'}
                                        modalProps={{ header: '编辑景点', size: 'xl' }}
                                        action={`spot/${record.id}`}
                                        method={'put'}
                                        onSuccess={action.reload}
                                        formData={record}
                                        buttonProps={{ as: Dropdown.Item }}
                                    />
                                    {/* 只有管理员可以禁用/启用景点 */}
                                    {isAdmin && (
                                        record.status === 1 ? (
                                            <RequestButton
                                                url={`spot/${record.id}/disable`}
                                                method={'put'}
                                                onSuccess={action.reload}
                                                as={Dropdown.Item}
                                            >禁用景点</RequestButton>
                                        ) : (
                                            <RequestButton
                                                url={`spot/${record.id}/enable`}
                                                method={'put'}
                                                onSuccess={action.reload}
                                                as={Dropdown.Item}
                                            >启用景点</RequestButton>
                                        )
                                    )}
                                    <RequestButton
                                        method={'post'}
                                        url={`spot/${record.id}/unpend`}
                                        onSuccess={action.reload}
                                        as={Dropdown.Item}
                                    >取消审核</RequestButton>
                                    {/* 只有管理员可以删除景点 */}
                                    {isAdmin && (
                                        <RequestButton
                                            url={`spot/${record.id}`}
                                            method={'delete'}
                                            onSuccess={action.reload}
                                            as={Dropdown.Item}
                                            confirm={`确定要删除景点 "${record.name}" 吗？此操作不可恢复！`}
                                        >删除景点</RequestButton>
                                    )}
                                </Dropdown.Menu>
                            </Dropdown>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
