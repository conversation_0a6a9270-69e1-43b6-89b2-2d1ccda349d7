import { Content, RequestButton, Space, Table } from '@topthink/common';

export const Component = () => {
    return <Content>
        <Table
            source={'spot/pending'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 80
                },
                {
                    title: '景点名称',
                    dataIndex: 'name',
                },
                {
                    title: '开放时间',
                    dataIndex: 'hours',
                    width: 120,
                    render({ value }) {
                        return value ? `${value.start}-${value.end}` : '-';
                    },
                },
                {
                    title: '区县',
                    dataIndex: ['location', 'district'],
                    width: 80
                },
                {
                    title: '地址',
                    dataIndex: 'address',
                    width: 250
                },
                {
                    title: '店主',
                    width: 200,
                    dataIndex: 'owner',
                    render({ value }) {
                        return value ? `${value.nickname}(${value.mobile})` : '-';
                    }
                },
                {
                    title: '操作',
                    width: 100,
                    render({ record, action }) {
                        return <Space>
                            <RequestButton method={'post'} url={`spot/${record.id}/approve`} confirm={'确认通过吗？'} onSuccess={action.reload}>通过</RequestButton>
                            <RequestButton method={'post'} url={`spot/${record.id}/reject`} confirm={'确认拒绝吗？'} onSuccess={action.reload}>拒绝</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
