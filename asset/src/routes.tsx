import { Navigate, RouteObject, TabLayout } from '@topthink/common';
import Layout from './layout';

const routes: RouteObject[] = [
    {
        element: <Layout />,
        children: [
            {
                index: true,
                element: <Navigate to='eatery' replace />
            },
            {
                path: 'user',
                meta: {
                    title: '用户管理',
                    icon: 'people'
                },
                lazy: () => import('./pages/user'),
            },
            {
                path: 'eatery',
                meta: {
                    title: '商家管理',
                    icon: 'shop'
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to='list' replace />
                    },
                    {
                        path: 'list',
                        lazy: () => import('./pages/eatery/list'),
                        meta: {
                            title: '商家列表',
                        }
                    },
                    {
                        path: 'review',
                        lazy: () => import('./pages/eatery/review'),
                        meta: {
                            title: '商家审核',
                        }
                    }
                ]
            },
            {
                path: 'spot',
                meta: {
                    title: '景点管理',
                    icon: 'geo-alt'
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to='list' replace />
                    },
                    {
                        path: 'list',
                        lazy: () => import('./pages/spot/list'),
                        meta: {
                            title: '景点列表',
                        }
                    },
                    {
                        path: 'review',
                        lazy: () => import('./pages/spot/review'),
                        meta: {
                            title: '景点审核',
                        }
                    }
                ]
            },
            {
                path: 'hotel',
                meta: {
                    title: '酒店管理',
                    icon: 'building'
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to='list' replace />
                    },
                    {
                        path: 'list',
                        lazy: () => import('./pages/hotel/list'),
                        meta: {
                            title: '酒店列表',
                        }
                    },
                    {
                        path: 'review',
                        lazy: () => import('./pages/hotel/review'),
                        meta: {
                            title: '酒店审核',
                        }
                    }
                ]
            },
            {
                path: 'coupon',
                meta: {
                    title: '消费券管理',
                    icon: 'cash-stack'
                },
                lazy: () => import('./pages/coupon'),
            },
            {
                path: 'system',
                meta: {
                    title: '系统管理',
                    icon: 'gear'
                },
                children: [
                    {
                        path: 'menu',
                        meta: {
                            title: '账号管理',
                            access: 60
                        },
                        lazy: () => import('./pages/system/admin'),
                    },
                    {
                        path: 'log',
                        meta: {
                            title: '日志管理',
                            hideChildrenInMenu: true
                        },
                        element: <TabLayout />,
                        children: [
                            {
                                index: true,
                                element: <Navigate to='admin' replace />
                            },
                            {
                                path: 'admin',
                                lazy: () => import('./pages/system/log/admin'),
                                meta: {
                                    title: '管理日志'
                                }
                            },
                            {
                                path: 'merchant',
                                lazy: () => import('./pages/system/log/merchant'),
                                meta: {
                                    title: '商户日志',
                                }
                            }
                        ]
                    },
                    {
                        path: 'setting',
                        meta: {
                            title: '系统设置',
                            hideChildrenInMenu: true,
                            access: 60
                        },
                        element: <TabLayout />,
                        children: [
                            {
                                index: true,
                                element: <Navigate to={'coupon'} replace />
                            },
                            {
                                path: 'coupon',
                                lazy: () => import('./pages/system/setting/coupon'),
                                meta: {
                                    title: '优惠券设置',
                                }
                            },
                        ]
                    }
                ]
            }
        ]
    }
];

export default routes;
