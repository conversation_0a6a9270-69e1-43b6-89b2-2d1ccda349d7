import { createApplication, Form, request, useNavigate } from '@topthink/common';
import './scss/app.scss';
import { createRoot } from 'react-dom/client';
import routes from './routes';

const container = document.getElementById('app');

if (container) {
    const root = createRoot(container);

    const App = createApplication({
        baseURL: '/admin',
        routes,
        async userResolver() {
            const user = await request('/auth/user');
            user.avatar = require('./images/avatar.png').default;
            return user;
        },
        onLogin({ onLogined }) {
            const navigate = useNavigate();
            return <>
                <h2 className='text-center mb-5'>塞上早茶管理平台</h2>
                <Form
                    action={'/auth/login'}
                    method={'post'}
                    schema={{
                        type: 'object',
                        properties: {
                            username: {
                                type: 'string',
                                title: '用户名'
                            },
                            password: {
                                type: 'string',
                                title: '密码',
                                format: 'password'
                            }
                        }
                    }}
                    onSuccess={({ token }) => {
                        const url = onLogined(token);
                        navigate(url);
                    }}
                    submitText={'登录'}
                    children={({ submit }) => {
                        return <div className='col-12 d-grid'>
                            {submit}
                        </div>;
                    }}
                />
            </>;
        },
    });

    root.render(<App />);
}
