{"name": "yunwuxin/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2.0", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0", "topthink/think-filesystem": "^3.0", "nesbot/carbon": "^2.64", "topthink/think-swoole": "^4.0", "topthink/think-migration": "^3.1", "topthink/think-annotation": "^2.0", "yunwuxin/think-auth": "^3.0", "firebase/php-jwt": "^6.11", "w7corp/easywechat": "^6.17", "endroid/qr-code": "^6.0", "hashids/hashids": "^5.0", "phpoffice/phpspreadsheet": "^4.2"}, "require-dev": {"topthink/think-dumper": "^1.0", "topthink/think-ide-helper": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}}, "config": {"preferred-install": "dist", "platform-check": false, "platform": {"ext-swoole": "4.6.0", "ext-fileinfo": "1.0.0", "ext-zip": "1", "ext-gd": "1"}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}