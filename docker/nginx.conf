server {
    listen  80;

    error_log /dev/stderr warn;
    access_log /dev/stdout main;
    client_max_body_size 20m;

    root  /opt/htdocs/asset/dist;

    location /asset/ {
        try_files $uri =404;
    }

    location /uploads/ {
        root  /opt/htdocs/storage;
        try_files $uri =404;
    }

    location / {
        try_files $uri /index.html;
    }

    location /api/ {
        proxy_pass http://127.0.0.1:8080/api/;
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_pass_header X-Accel-Buffering;
    }

    location /admin/ {
        proxy_pass http://127.0.0.1:8080/admin/;
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
