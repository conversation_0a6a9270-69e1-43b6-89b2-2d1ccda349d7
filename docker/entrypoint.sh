#!/bin/bash
set -e

#确保目录权限
chown www-data:www-data /opt/htdocs/storage

case ${1} in
  app:init)
    su -c "php think migrate:run" www-data
    su -c "php think app:init" www-data
    ;;
  app:start)
    rm -rf /var/run/supervisor.sock
    exec /usr/bin/supervisord -nc /etc/supervisor/supervisord.conf
    ;;
  app:run)
    su -c "php think migrate:run" www-data
    su -c "php think app:init" www-data
    rm -rf /var/run/supervisor.sock
    exec /usr/bin/supervisord -nc /etc/supervisor/supervisord.conf
    ;;
  *)
    exec "$@"
    ;;
esac
